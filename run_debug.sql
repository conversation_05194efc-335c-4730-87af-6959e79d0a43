-- Quick debug to check if we have any status periods at all
WITH
operators as (
 SELECT
   *
 FROM (
   SELECT
     *,
     ROW_NUMBER() OVER (PARTITION BY operator_code ORDER BY __raw_message_ingestion_time DESC) AS rn
   FROM
     `edp-p-us-east1-etl.acehardware_jeffersonga.gold_operator` 
 )
 WHERE rn = 1
),
-- Base activity events
base_activity AS (
SELECT
  TIMESTAMP(event_date_time_local) AS record_timestamp,
  event_code,
  workstation_code,
  operator_code,
  source_system
FROM
  `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick_activity` pa
WHERE
  REGEXP_CONTAINS(workstation_code, r'GTP[0-9][0-9].*')
  AND event_timestamp_utc >= TIMESTAMP(DATETIME(DATE_SUB(CURRENT_DATE(), INTERVAL 2 DAY), TIME '00:00:00'))
  AND operator_name IS NOT NULL
  AND operator_name != '' 
  and event_code IN ('LOGON', 'LOGOFF') 
  AND workstation_code = 'GTP01'
),
-- Add operator names
activity_with_operators AS (
SELECT
  ba.record_timestamp,
  ba.event_code,
  ba.workstation_code,
  ba.source_system,
  op.operator_name,
  ROW_NUMBER() OVER (
    PARTITION BY ba.workstation_code 
    ORDER BY ba.record_timestamp ASC
  ) AS event_sequence
FROM
  base_activity ba
LEFT JOIN
  operators op
ON
  ba.operator_code = op.operator_code
),
-- Check operator login state
operator_states AS (
SELECT
  record_timestamp,
  event_code,
  workstation_code,
  source_system,
  operator_name,
  -- Track operator login state
  COALESCE(
    LAST_VALUE(
      CASE 
        WHEN event_code = 'LOGON' THEN TRUE
        WHEN event_code = 'LOGOFF' THEN FALSE
        ELSE NULL
      END IGNORE NULLS
    ) OVER (
      PARTITION BY workstation_code
      ORDER BY record_timestamp ASC
      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
    ),
    FALSE
  ) AS operator_logged_in,
  -- Calculate end timestamp
  LEAD(record_timestamp) OVER (
    PARTITION BY workstation_code
    ORDER BY record_timestamp ASC
  ) AS end_timestamp
FROM activity_with_operators
),
-- Create status periods
status_periods AS (
SELECT
  record_timestamp AS start_timestamp,
  COALESCE(end_timestamp, TIMESTAMP_ADD(record_timestamp, INTERVAL 4 HOUR)) AS end_timestamp,
  workstation_code,
  operator_name,
  source_system,
  CASE 
    WHEN operator_logged_in THEN 'Starved'  -- Default status when logged in
    ELSE NULL
  END AS status
FROM operator_states
WHERE operator_logged_in = TRUE
  AND operator_name IS NOT NULL
)

SELECT 
  workstation_code,
  status,
  COUNT(*) as period_count,
  MIN(start_timestamp) as earliest_start,
  MAX(end_timestamp) as latest_end,
  STRING_AGG(DISTINCT operator_name) as operators
FROM status_periods 
GROUP BY workstation_code, status
ORDER BY workstation_code, status;
