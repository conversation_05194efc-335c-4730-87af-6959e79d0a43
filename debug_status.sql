-- Debug query to check status calculation
WITH
operators as (
 SELECT
   *
 FROM (
   SELECT
     *,
     ROW_NUMBER() OVER (PARTITION BY operator_code ORDER BY __raw_message_ingestion_time DESC) AS rn
   FROM
     `edp-p-us-east1-etl.acehardware_jeffersonga.gold_operator` 
 )
 WHERE rn = 1
),
-- Base activity events
base_activity AS (
SELECT
  TIMESTAMP(event_date_time_local) AS record_timestamp,
  event_code,
  workstation_code,
  work_type_code,
  CASE
    WHEN STARTS_WITH(workstation_code, 'GTP') THEN 'GTP'
    WHEN STARTS_WITH(workstation_code, 'CS') THEN 'QA'
    WHEN STARTS_WITH(workstation_code, 'DEC') THEN 'DECANT'
    ELSE 'UNKNOWN'
  END AS workstation_type,
  operator_code,
  IF(IFNULL(handling_unit_code, '') = '', 'UFO', handling_unit_code) AS tote_code,
  source_system,
  __raw_message_ingestion_time
FROM
  `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick_activity` pa
WHERE
  REGEXP_CONTAINS(workstation_code, r'GTP[0-9][0-9].*')
  AND event_timestamp_utc >= TIMESTAMP(DATETIME(DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY), TIME '00:00:00'))
  AND (
      -- For LOGON/LOGOFF events, require operator info
      (event_code IN ('LOGON', 'LOGOFF') AND operator_name IS NOT NULL AND operator_name != '')
      OR
      -- For tote events, don't require operator info (will be filled later)
      (event_code IN ('Arrival', 'Departure', 'Release') AND left(handling_unit_code,1)='D')
  )
  AND workstation_code = 'GTP01'  -- Focus on one workstation for testing
),
-- Add operator names and create session groups
activity_events_with_sessions AS (
SELECT
  ba.record_timestamp,
  ba.event_code,
  ba.workstation_code,
  ba.work_type_code,
  ba.workstation_type,
  ba.tote_code,
  ba.source_system,
  ba.__raw_message_ingestion_time,
  op.operator_name,    
  ROW_NUMBER() OVER (
    PARTITION BY ba.workstation_code 
    ORDER BY ba.record_timestamp ASC, CASE WHEN ba.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
  ) AS event_sequence
FROM
  base_activity ba
LEFT JOIN
  operators op
ON
  ba.operator_code = op.operator_code
),
-- Simple tote counting (just to verify the counts are working)
simple_counts AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    operator_name,
    -- Simple running count of arrivals minus departures
    SUM(CASE WHEN event_code = 'Arrival' THEN 1 
             WHEN event_code = 'Departure' THEN -1 
             ELSE 0 END) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS simple_arr_count,
    -- Simple running count of releases minus departures
    SUM(CASE WHEN event_code = 'Release' THEN 1 
             WHEN event_code = 'Departure' THEN -1 
             ELSE 0 END) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS simple_rel_count
  FROM activity_events_with_sessions
),
-- Add operator logic
operator_logic AS (
SELECT
  sc.record_timestamp,
  sc.event_code,
  sc.workstation_code,
  sc.operator_name,
  sc.simple_arr_count,
  sc.simple_rel_count,
  -- Fill operator names using window functions
  COALESCE(
    sc.operator_name,
    LAST_VALUE(
      CASE WHEN sc.event_code = 'LOGON' AND sc.operator_name IS NOT NULL THEN sc.operator_name END 
      IGNORE NULLS
    ) OVER (PARTITION BY sc.workstation_code
      ORDER BY sc.record_timestamp ASC 
      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
    )
  ) AS filled_operator_name,
  -- Calculate end timestamp for each event
  LEAD(sc.record_timestamp) OVER (
    PARTITION BY sc.workstation_code
    ORDER BY sc.record_timestamp ASC, 
             CASE WHEN sc.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
  ) AS end_timestamp,
  -- Track operator login state per session
  COALESCE(
    LAST_VALUE(
      CASE 
        WHEN sc.event_code = 'LOGON' THEN TRUE
        WHEN sc.event_code = 'LOGOFF' THEN FALSE
        ELSE NULL
      END IGNORE NULLS
    ) OVER (
      PARTITION BY sc.workstation_code
      ORDER BY sc.record_timestamp ASC, 
               CASE WHEN sc.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
    ),
    FALSE
  ) AS operator_logged_in,
  -- Track next event for no-action detection
  LEAD(sc.event_code) OVER (
    PARTITION BY sc.workstation_code
    ORDER BY sc.record_timestamp ASC, 
             CASE WHEN sc.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
  ) AS next_event
FROM simple_counts sc
),
-- Apply status calculation
status_calc AS (
SELECT
  record_timestamp,
  end_timestamp,
  workstation_code,
  filled_operator_name,
  simple_arr_count,
  simple_rel_count,
  operator_logged_in,
  event_code,
  next_event,
  CASE
    WHEN NOT operator_logged_in THEN NULL
    -- Handle no action case: LOGON immediately followed by LOGOFF
    WHEN event_code = 'LOGON' AND next_event = 'LOGOFF' THEN 'No Action'
    -- SQL Server trigger logic: IF @in_LU_ArrCount > 0 THEN 'Tote Present'
    WHEN operator_logged_in AND simple_arr_count > 0 THEN 'Tote Present'
    -- SQL Server trigger logic: ELSE IF @in_LU_RelCount > 0 THEN 'Blocked'  
    WHEN operator_logged_in AND simple_rel_count > 0 THEN 'Blocked'
    -- SQL Server trigger logic: ELSE IF @in_LU_RelCount = 0 AND @in_LU_ArrCount = 0 THEN 'Starved'
    WHEN operator_logged_in AND simple_rel_count = 0 AND simple_arr_count = 0 THEN 'Starved'
    ELSE 'Unknown'
  END AS status
FROM operator_logic
)

-- Show the status progression
SELECT 
  record_timestamp,
  event_code,
  filled_operator_name,
  operator_logged_in,
  simple_arr_count,
  simple_rel_count,
  status,
  TIMESTAMP_DIFF(COALESCE(end_timestamp, TIMESTAMP_ADD(record_timestamp, INTERVAL 1 HOUR)), record_timestamp, SECOND) as duration_seconds
FROM status_calc
WHERE record_timestamp >= '2025-07-25 00:00:00'
  AND record_timestamp < '2025-07-26 00:00:00'
ORDER BY record_timestamp
LIMIT 50;
