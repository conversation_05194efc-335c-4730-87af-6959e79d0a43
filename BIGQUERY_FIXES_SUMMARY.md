# BigQuery Query Fixes - Summary of Changes

## Problem Identified
The original BigQuery implementation had incorrect tote counting logic that didn't match the SQL Server trigger behavior, causing discrepancies in:
- `Starved_Time_Sec`
- `Blocked_Time_Sec` 
- `Presented_Time_Sec`
- `No_Action_Time_Sec`

## Root Cause
The main issue was in the `tote_state_transitions` CTE where the Release logic was:
```sql
WHEN event_code = 'Release' AND previous_tote_event = 'Arrival' THEN 'Released'
```

This only checked if the immediately previous event was 'Arrival', but didn't validate whether the tote was currently in an "arrived" state (could have departed and returned).

## Key Changes Made

### 1. Completely Rewrote Tote State Calculation (Lines 118-268)
- **Removed**: The flawed delta-based approach with "Gone" states
- **Added**: Proper state tracking that matches SQL Server logic
- **Fixed**: Release events now only count when tote is currently in 'Arrived' state

### 2. Implemented SQL Server Logic Exactly
The new logic replicates the SQL Server trigger behavior:

**Arrival Event:**
- Adds tote to arrival list (lu_arr_count +1)

**Release Event:** 
- Only processes if tote is currently 'Arrived'
- Removes from arrival list (lu_arr_count -1)
- Adds to release list (lu_rel_count +1)

**Departure Event:**
- Removes from both arrival and release lists
- Decrements appropriate counts

### 3. Fixed Status Determination (Lines 321-347)
Updated status names to match SQL Server exactly:
- `'starvation'` → `'Starved'`
- `'blocking'` → `'Blocked'` 
- `'tote_present'` → `'Tote Present'`
- `'no_action'` → `'No Action'`

### 4. Improved State Validation
Added proper validation using `LAST_VALUE()` with `IGNORE NULLS` to track the actual current state of each tote, ensuring:
- Release events only count when tote is actually arrived
- Departure events properly clean up both arrival and release counts
- No negative counts or invalid state transitions

## Expected Results
With these fixes, the BigQuery implementation should now produce:
- `lu_arr_count` values that match SQL Server `LU_Arr_Count`
- `lu_rel_count` values that match SQL Server `LU_Rel_Count`
- Accurate time calculations for Starved, Blocked, Presented, and No Action states
- Results that align with the SQL Server output

## Validation Recommendations
1. Compare `lu_arr_count` and `lu_rel_count` values between systems
2. Verify time field calculations match
3. Check that status transitions follow the same logic as SQL Server
4. Ensure no negative counts occur in the BigQuery results
