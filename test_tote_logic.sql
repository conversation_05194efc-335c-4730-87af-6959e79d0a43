-- Test query to verify tote counting logic
WITH
operators as (
 SELECT
   *
 FROM (
   SELECT
     *,
     ROW_NUMBER() OVER (PARTITION BY operator_code ORDER BY __raw_message_ingestion_time DESC) AS rn
   FROM
     `edp-p-us-east1-etl.acehardware_jeffersonga.gold_operator` 
 )
 WHERE rn = 1
),
-- Base activity events
base_activity AS (
SELECT
  TIMESTAMP(event_date_time_local) AS record_timestamp,
  event_code,
  workstation_code,
  work_type_code,
  CASE
    WHEN STARTS_WITH(workstation_code, 'GTP') THEN 'GTP'
    WHEN STARTS_WITH(workstation_code, 'CS') THEN 'QA'
    WHEN STARTS_WITH(workstation_code, 'DEC') THEN 'DECANT'
    ELSE 'UNKNOWN'
  END AS workstation_type,
  operator_code,
  IF(IFNULL(handling_unit_code, '') = '', 'UFO', handling_unit_code) AS tote_code,
  source_system,
  __raw_message_ingestion_time
FROM
  `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick_activity` pa
WHERE
  REGEXP_CONTAINS(workstation_code, r'GTP[0-9][0-9].*')
  AND event_timestamp_utc >= TIMESTAMP(DATETIME(DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY), TIME '00:00:00'))
  AND (
      -- For LOGON/LOGOFF events, require operator info
      (event_code IN ('LOGON', 'LOGOFF') AND operator_name IS NOT NULL AND operator_name != '')
      OR
      -- For tote events, don't require operator info (will be filled later)
      (event_code IN ('Arrival', 'Departure', 'Release') AND left(handling_unit_code,1)='D')
  )
  AND workstation_code = 'GTP01'  -- Focus on one workstation for testing
),
-- Add operator names and create session groups
activity_events_with_sessions AS (
SELECT
  ba.record_timestamp,
  ba.event_code,
  ba.workstation_code,
  ba.work_type_code,
  ba.workstation_type,
  ba.tote_code,
  ba.source_system,
  ba.__raw_message_ingestion_time,
  op.operator_name,    
  ROW_NUMBER() OVER (
    PARTITION BY ba.workstation_code 
    ORDER BY ba.record_timestamp ASC, CASE WHEN ba.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
  ) AS event_sequence
FROM
  base_activity ba
LEFT JOIN
  operators op
ON
  ba.operator_code = op.operator_code
),
-- Get tote events and track previous state
tote_events AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    tote_code,
    event_sequence
  FROM activity_events_with_sessions
  WHERE event_code IN ('Arrival', 'Release', 'Departure')
),
-- Track each tote's state over time (step by step)
tote_state_step1 AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    tote_code,
    event_sequence,
    -- Previous state of this tote
    LAG(
      CASE 
        WHEN event_code = 'Arrival' THEN 'in_arrival_list'
        WHEN event_code = 'Departure' THEN 'gone'
        WHEN event_code = 'Release' THEN 'in_release_list'
      END
    ) OVER (
      PARTITION BY workstation_code, tote_code 
      ORDER BY record_timestamp, event_sequence
    ) AS previous_tote_state
  FROM tote_events
),
-- Determine valid state transitions (SQL Server logic)
tote_state_transitions AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    tote_code,
    event_sequence,
    previous_tote_state,
    -- Current state after this event (following SQL Server rules)
    CASE 
      WHEN event_code = 'Arrival' THEN 'in_arrival_list'
      WHEN event_code = 'Departure' THEN 'gone'
      WHEN event_code = 'Release' AND previous_tote_state = 'in_arrival_list' THEN 'in_release_list'
      WHEN event_code = 'Release' AND previous_tote_state != 'in_arrival_list' THEN previous_tote_state  -- Invalid release
      ELSE COALESCE(previous_tote_state, 'gone')
    END AS current_tote_state
  FROM tote_state_step1
),
-- Calculate count changes for each event (exact SQL Server logic)
count_deltas AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    event_sequence,
    -- Arrival count changes (LU_Arr_Count logic)
    SUM(
      CASE 
        WHEN event_code = 'Arrival' THEN 1
        WHEN event_code = 'Release' AND previous_tote_state = 'in_arrival_list' THEN -1
        WHEN event_code = 'Departure' AND previous_tote_state = 'in_arrival_list' THEN -1
        ELSE 0
      END
    ) AS delta_arr_count,
    -- Release count changes (LU_Rel_Count logic)
    SUM(
      CASE 
        WHEN event_code = 'Release' AND previous_tote_state = 'in_arrival_list' THEN 1
        WHEN event_code = 'Departure' AND previous_tote_state = 'in_release_list' THEN -1
        ELSE 0
      END
    ) AS delta_rel_count
  FROM tote_state_transitions
  GROUP BY record_timestamp, event_code, workstation_code, event_sequence
),
-- Merge deltas with all events and calculate running totals
events_with_counts AS (
  SELECT
    aews.record_timestamp,
    aews.event_code,
    aews.workstation_code,
    aews.work_type_code,
    aews.workstation_type,
    aews.source_system,
    aews.operator_name,
    aews.event_sequence,
    COALESCE(cd.delta_arr_count, 0) AS delta_arr_count,
    COALESCE(cd.delta_rel_count, 0) AS delta_rel_count
  FROM activity_events_with_sessions aews
  LEFT JOIN count_deltas cd
    ON aews.record_timestamp = cd.record_timestamp
    AND aews.event_code = cd.event_code
    AND aews.workstation_code = cd.workstation_code
    AND aews.event_sequence = cd.event_sequence
),
-- Calculate running tote counts (SQL Server LU_Arr_Count and LU_Rel_Count)
tote_counts AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    work_type_code,
    workstation_type,
    source_system,
    operator_name,
    delta_arr_count,
    delta_rel_count,
    -- Running arrival count (matches SQL Server LU_Arr_Count)
    SUM(delta_arr_count) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS lu_arr_count,
    -- Running release count (matches SQL Server LU_Rel_Count)
    SUM(delta_rel_count) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS lu_rel_count
  FROM events_with_counts
)

-- Test output showing tote count progression
SELECT 
  record_timestamp,
  event_code,
  workstation_code,
  operator_name,
  delta_arr_count,
  delta_rel_count,
  lu_arr_count,
  lu_rel_count,
  -- Status based on counts (SQL Server trigger logic)
  CASE 
    WHEN lu_arr_count > 0 THEN 'Tote Present'
    WHEN lu_rel_count > 0 THEN 'Blocked'
    WHEN lu_arr_count = 0 AND lu_rel_count = 0 THEN 'Starved'
    ELSE 'Unknown'
  END AS calculated_status
FROM tote_counts
WHERE record_timestamp >= '2025-07-25 00:00:00'
  AND record_timestamp < '2025-07-26 00:00:00'
ORDER BY record_timestamp, event_sequence
LIMIT 100;
