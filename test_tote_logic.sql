-- Test query to verify tote counting logic
WITH
operators as (
 SELECT
   *
 FROM (
   SELECT
     *,
     ROW_NUMBER() OVER (PARTITION BY operator_code ORDER BY __raw_message_ingestion_time DESC) AS rn
   FROM
     `edp-p-us-east1-etl.acehardware_jeffersonga.gold_operator` 
 )
 WHERE rn = 1
),
-- Base activity events
base_activity AS (
SELECT
  TIMESTAMP(event_date_time_local) AS record_timestamp,
  event_code,
  workstation_code,
  work_type_code,
  CASE
    WHEN STARTS_WITH(workstation_code, 'GTP') THEN 'GTP'
    WHEN STARTS_WITH(workstation_code, 'CS') THEN 'QA'
    WHEN STARTS_WITH(workstation_code, 'DEC') THEN 'DECANT'
    ELSE 'UNKNOWN'
  END AS workstation_type,
  operator_code,
  IF(IFNULL(handling_unit_code, '') = '', 'UFO', handling_unit_code) AS tote_code,
  source_system,
  __raw_message_ingestion_time
FROM
  `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick_activity` pa
WHERE
  REGEXP_CONTAINS(workstation_code, r'GTP[0-9][0-9].*')
  AND event_timestamp_utc >= TIMESTAMP(DATETIME(DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY), TIME '00:00:00'))
  AND (
      -- For LOGON/LOGOFF events, require operator info
      (event_code IN ('LOGON', 'LOGOFF') AND operator_name IS NOT NULL AND operator_name != '')
      OR
      -- For tote events, don't require operator info (will be filled later)
      (event_code IN ('Arrival', 'Departure', 'Release') AND left(handling_unit_code,1)='D')
  )
  AND workstation_code = 'GTP01'  -- Focus on one workstation for testing
),
-- Add operator names and create session groups
activity_events_with_sessions AS (
SELECT
  ba.record_timestamp,
  ba.event_code,
  ba.workstation_code,
  ba.work_type_code,
  ba.workstation_type,
  ba.tote_code,
  ba.source_system,
  ba.__raw_message_ingestion_time,
  op.operator_name,    
  ROW_NUMBER() OVER (
    PARTITION BY ba.workstation_code 
    ORDER BY ba.record_timestamp ASC, CASE WHEN ba.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
  ) AS event_sequence
FROM
  base_activity ba
LEFT JOIN
  operators op
ON
  ba.operator_code = op.operator_code
),
-- SIMPLIFIED APPROACH: Direct count calculation matching SQL Server exactly
-- 1. For each tote event, determine what it does to the workstation counts
tote_count_impacts AS (
  SELECT
    aews.record_timestamp,
    aews.event_code,
    aews.workstation_code,
    aews.event_sequence,
    aews.tote_code,
    -- Determine if this tote was previously at the station
    CASE
      WHEN aews.event_code = 'Release' OR aews.event_code = 'Departure' THEN
        -- Check if this tote had a previous Arrival without a Departure
        CASE
          WHEN EXISTS (
            SELECT 1
            FROM activity_events_with_sessions prev
            WHERE prev.workstation_code = aews.workstation_code
              AND prev.tote_code = aews.tote_code
              AND prev.event_code = 'Arrival'
              AND prev.record_timestamp < aews.record_timestamp
              AND NOT EXISTS (
                SELECT 1
                FROM activity_events_with_sessions dep
                WHERE dep.workstation_code = aews.workstation_code
                  AND dep.tote_code = aews.tote_code
                  AND dep.event_code = 'Departure'
                  AND dep.record_timestamp > prev.record_timestamp
                  AND dep.record_timestamp < aews.record_timestamp
              )
          ) THEN TRUE
          ELSE FALSE
        END
      ELSE FALSE
    END AS tote_was_at_station,

    -- Determine if this tote was previously released (for departure events)
    CASE
      WHEN aews.event_code = 'Departure' THEN
        CASE
          WHEN EXISTS (
            SELECT 1
            FROM activity_events_with_sessions rel
            WHERE rel.workstation_code = aews.workstation_code
              AND rel.tote_code = aews.tote_code
              AND rel.event_code = 'Release'
              AND rel.record_timestamp < aews.record_timestamp
              AND EXISTS (
                SELECT 1
                FROM activity_events_with_sessions arr
                WHERE arr.workstation_code = aews.workstation_code
                  AND arr.tote_code = aews.tote_code
                  AND arr.event_code = 'Arrival'
                  AND arr.record_timestamp < rel.record_timestamp
                  AND NOT EXISTS (
                    SELECT 1
                    FROM activity_events_with_sessions dep2
                    WHERE dep2.workstation_code = aews.workstation_code
                      AND dep2.tote_code = aews.tote_code
                      AND dep2.event_code = 'Departure'
                      AND dep2.record_timestamp > arr.record_timestamp
                      AND dep2.record_timestamp < rel.record_timestamp
                  )
              )
          ) THEN TRUE
          ELSE FALSE
        END
      ELSE FALSE
    END AS tote_was_released
  FROM activity_events_with_sessions aews
  WHERE aews.event_code IN ('Arrival', 'Release', 'Departure')
),

-- 2. Calculate the count deltas based on SQL Server logic
count_deltas AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    event_sequence,
    -- LU_Arr_Count changes (SQL Server logic)
    SUM(
      CASE
        WHEN event_code = 'Arrival' THEN 1
        WHEN event_code = 'Release' AND tote_was_at_station THEN -1
        WHEN event_code = 'Departure' AND tote_was_at_station AND NOT tote_was_released THEN -1
        ELSE 0
      END
    ) AS delta_arr_count,
    -- LU_Rel_Count changes (SQL Server logic)
    SUM(
      CASE
        WHEN event_code = 'Release' AND tote_was_at_station THEN 1
        WHEN event_code = 'Departure' AND tote_was_released THEN -1
        ELSE 0
      END
    ) AS delta_rel_count
  FROM tote_count_impacts
  GROUP BY record_timestamp, event_code, workstation_code, event_sequence
),
-- Merge deltas with all events and calculate running totals
events_with_counts AS (
  SELECT
    aews.record_timestamp,
    aews.event_code,
    aews.workstation_code,
    aews.work_type_code,
    aews.workstation_type,
    aews.source_system,
    aews.operator_name,
    aews.event_sequence,
    COALESCE(cd.delta_arr_count, 0) AS delta_arr_count,
    COALESCE(cd.delta_rel_count, 0) AS delta_rel_count
  FROM activity_events_with_sessions aews
  LEFT JOIN count_deltas cd
    ON aews.record_timestamp = cd.record_timestamp
    AND aews.event_code = cd.event_code
    AND aews.workstation_code = cd.workstation_code
    AND aews.event_sequence = cd.event_sequence
),
-- Calculate running tote counts (SQL Server LU_Arr_Count and LU_Rel_Count)
tote_counts AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    work_type_code,
    workstation_type,
    source_system,
    operator_name,
    delta_arr_count,
    delta_rel_count,
    -- Running arrival count (matches SQL Server LU_Arr_Count)
    SUM(delta_arr_count) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS lu_arr_count,
    -- Running release count (matches SQL Server LU_Rel_Count)
    SUM(delta_rel_count) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS lu_rel_count
  FROM events_with_counts
)

-- Test output showing tote count progression
SELECT 
  record_timestamp,
  event_code,
  workstation_code,
  operator_name,
  delta_arr_count,
  delta_rel_count,
  lu_arr_count,
  lu_rel_count,
  -- Status based on counts (SQL Server trigger logic)
  CASE 
    WHEN lu_arr_count > 0 THEN 'Tote Present'
    WHEN lu_rel_count > 0 THEN 'Blocked'
    WHEN lu_arr_count = 0 AND lu_rel_count = 0 THEN 'Starved'
    ELSE 'Unknown'
  END AS calculated_status
FROM tote_counts
WHERE record_timestamp >= '2025-07-25 00:00:00'
  AND record_timestamp < '2025-07-26 00:00:00'
ORDER BY record_timestamp, event_sequence
LIMIT 100;
