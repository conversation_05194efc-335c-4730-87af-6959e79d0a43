-- Step by step debug to see exactly what's happening
WITH
operators as (
 SELECT
   *
 FROM (
   SELECT
     *,
     ROW_NUMBER() OVER (PARTITION BY operator_code ORDER BY __raw_message_ingestion_time DESC) AS rn
   FROM
     `edp-p-us-east1-etl.acehardware_jeffersonga.gold_operator` 
 )
 WHERE rn = 1
),
-- Base activity events
base_activity AS (
SELECT
  TIMESTAMP(event_date_time_local) AS record_timestamp,
  event_code,
  workstation_code,
  operator_code,
  IF(IFNULL(handling_unit_code, '') = '', 'UFO', handling_unit_code) AS tote_code,
  source_system
FROM
  `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick_activity` pa
WHERE
  REGEXP_CONTAINS(workstation_code, r'GTP[0-9][0-9].*')
  AND event_timestamp_utc >= TIMESTAMP(DATETIME(DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY), TIME '00:00:00'))
  AND (
      (event_code IN ('LOGON', 'LOGOFF') AND operator_name IS NOT NULL AND operator_name != '')
      OR
      (event_code IN ('Arrival', 'Departure', 'Release') AND left(handling_unit_code,1)='D')
  )
  AND workstation_code = 'GTP01'
),
-- Add operator names and create session groups
activity_events_with_sessions AS (
SELECT
  ba.record_timestamp,
  ba.event_code,
  ba.workstation_code,
  ba.tote_code,
  ba.source_system,
  op.operator_name,    
  ROW_NUMBER() OVER (
    PARTITION BY ba.workstation_code 
    ORDER BY ba.record_timestamp ASC, CASE WHEN ba.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
  ) AS event_sequence
FROM
  base_activity ba
LEFT JOIN
  operators op
ON
  ba.operator_code = op.operator_code
),
-- Show first 20 events to see what we're working with
first_events AS (
  SELECT 
    record_timestamp,
    event_code,
    tote_code,
    operator_name,
    event_sequence,
    -- Calculate deltas for each individual event
    CASE 
      WHEN event_code = 'Arrival' THEN 1
      WHEN event_code = 'Departure' THEN -1
      ELSE 0
    END AS arr_delta,
    CASE 
      WHEN event_code = 'Release' THEN 1
      WHEN event_code = 'Departure' THEN -1
      ELSE 0
    END AS rel_delta
  FROM activity_events_with_sessions
  WHERE record_timestamp >= '2025-07-25 00:00:00'
    AND record_timestamp < '2025-07-26 00:00:00'
  ORDER BY record_timestamp, event_sequence
  LIMIT 20
),
-- Calculate running totals
running_totals AS (
  SELECT 
    *,
    -- Running sum with baseline
    GREATEST(0, 100 + SUM(arr_delta) OVER (ORDER BY record_timestamp, event_sequence ROWS UNBOUNDED PRECEDING)) AS running_arr_count,
    GREATEST(0, 50 + SUM(rel_delta) OVER (ORDER BY record_timestamp, event_sequence ROWS UNBOUNDED PRECEDING)) AS running_rel_count
  FROM first_events
)

-- Show the progression
SELECT 
  record_timestamp,
  event_code,
  tote_code,
  operator_name,
  arr_delta,
  rel_delta,
  running_arr_count,
  running_rel_count,
  CASE 
    WHEN running_arr_count > 0 THEN 'Tote Present'
    WHEN running_rel_count > 0 THEN 'Blocked'
    WHEN running_arr_count = 0 AND running_rel_count = 0 THEN 'Starved'
    ELSE 'Unknown'
  END AS expected_status
FROM running_totals
ORDER BY record_timestamp, event_sequence;
