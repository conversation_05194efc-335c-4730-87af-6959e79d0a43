-- Debug query to check status calculation
WITH
operators as (
 SELECT
   *
 FROM (
   SELECT
     *,
     ROW_NUMBER() OVER (PARTITION BY operator_code ORDER BY __raw_message_ingestion_time DESC) AS rn
   FROM
     `edp-p-us-east1-etl.acehardware_jeffersonga.gold_operator` 
 )
 WHERE rn = 1
),
-- Base activity events
base_activity AS (
SELECT
  TIMESTAMP(event_date_time_local) AS record_timestamp,
  event_code,
  workstation_code,
  work_type_code,
  CASE
    WHEN STARTS_WITH(workstation_code, 'GTP') THEN 'GTP'
    WHEN STARTS_WITH(workstation_code, 'CS') THEN 'QA'
    WHEN STARTS_WITH(workstation_code, 'DEC') THEN 'DECANT'
    ELSE 'UNKNOWN'
  END AS workstation_type,
  operator_code,
  IF(IFNULL(handling_unit_code, '') = '', 'UFO', handling_unit_code) AS tote_code,
  source_system,
  __raw_message_ingestion_time
FROM
  `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick_activity` pa
WHERE
  REGEXP_CONTAINS(workstation_code, r'GTP[0-9][0-9].*')
  AND event_timestamp_utc >= TIMESTAMP(DATETIME(DATE_SUB(CURRENT_DATE(), INTERVAL 2 DAY), TIME '00:00:00'))
  AND operator_name IS NOT NULL
  AND operator_name != '' 
  and 
  (
      event_code IN ('LOGON', 'LOGOFF') 
  or
  event_code IN ('Arrival', 'Departure', 'Release') and left(handling_unit_code,1)='D'  
  )
),
-- Add operator names and create session groups
activity_events_with_sessions AS (
SELECT
  ba.record_timestamp,
  ba.event_code,
  ba.workstation_code,
  ba.work_type_code,
  ba.workstation_type,
  ba.tote_code,
  ba.source_system,
  ba.__raw_message_ingestion_time,
  op.operator_name,    
  ROW_NUMBER() OVER (
    PARTITION BY ba.workstation_code 
    ORDER BY ba.record_timestamp ASC, CASE WHEN ba.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
  ) AS event_sequence
FROM
  base_activity ba
LEFT JOIN
  operators op
ON
  ba.operator_code = op.operator_code
),
-- Simple tote counting for debugging
simple_tote_counts AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    work_type_code,
    workstation_type,
    source_system,
    operator_name,
    -- Simple running count of arrivals
    SUM(CASE WHEN event_code = 'Arrival' THEN 1 ELSE 0 END) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS total_arrivals,
    -- Simple running count of departures  
    SUM(CASE WHEN event_code = 'Departure' THEN 1 ELSE 0 END) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS total_departures,
    -- Simple arrival count (arrivals - departures)
    SUM(CASE WHEN event_code = 'Arrival' THEN 1 
             WHEN event_code = 'Departure' THEN -1 
             ELSE 0 END) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS simple_arr_count
  FROM activity_events_with_sessions
)

-- Debug output
SELECT 
  workstation_code,
  event_code,
  record_timestamp,
  operator_name,
  total_arrivals,
  total_departures,
  simple_arr_count,
  CASE 
    WHEN simple_arr_count > 0 THEN 'Tote Present'
    WHEN simple_arr_count = 0 THEN 'Starved'
    ELSE 'Unknown'
  END AS simple_status
FROM simple_tote_counts
WHERE workstation_code = 'GTP01'
  AND record_timestamp >= '2025-07-25 00:00:00'
  AND record_timestamp < '2025-07-26 00:00:00'
ORDER BY record_timestamp
LIMIT 50;
