-- Focused debug to find why starved/blocked are zero
WITH
operators as (
 SELECT
   *
 FROM (
   SELECT
     *,
     ROW_NUMBER() OVER (PARTITION BY operator_code ORDER BY __raw_message_ingestion_time DESC) AS rn
   FROM
     `edp-p-us-east1-etl.acehardware_jeffersonga.gold_operator` 
 )
 WHERE rn = 1
),
-- Base activity events
base_activity AS (
SELECT
  TIMESTAMP(event_date_time_local) AS record_timestamp,
  event_code,
  workstation_code,
  work_type_code,
  operator_code,
  IF(IFNULL(handling_unit_code, '') = '', 'UFO', handling_unit_code) AS tote_code,
  source_system
FROM
  `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick_activity` pa
WHERE
  REGEXP_CONTAINS(workstation_code, r'GTP[0-9][0-9].*')
  AND event_timestamp_utc >= TIMESTAMP(DATETIME(DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY), TIME '00:00:00'))
  AND (
      (event_code IN ('LOGON', 'LOGOFF') AND operator_name IS NOT NULL AND operator_name != '')
      OR
      (event_code IN ('Arrival', 'Departure', 'Release') AND left(handling_unit_code,1)='D')
  )
  AND workstation_code = 'GTP01'
),
-- Add operator names and create session groups
activity_events_with_sessions AS (
SELECT
  ba.record_timestamp,
  ba.event_code,
  ba.workstation_code,
  ba.work_type_code,
  ba.tote_code,
  ba.source_system,
  op.operator_name,    
  ROW_NUMBER() OVER (
    PARTITION BY ba.workstation_code 
    ORDER BY ba.record_timestamp ASC, CASE WHEN ba.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
  ) AS event_sequence
FROM
  base_activity ba
LEFT JOIN
  operators op
ON
  ba.operator_code = op.operator_code
),
-- Simple tote counting to verify basic logic
simple_tote_counts AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    tote_code,
    operator_name,
    event_sequence,
    -- Simple arrival count (arrivals - departures)
    SUM(CASE WHEN event_code = 'Arrival' THEN 1 
             WHEN event_code = 'Departure' THEN -1 
             ELSE 0 END) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS simple_arr_count,
    -- Simple release count (releases - departures)  
    SUM(CASE WHEN event_code = 'Release' THEN 1 
             WHEN event_code = 'Departure' THEN -1 
             ELSE 0 END) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS simple_rel_count
  FROM activity_events_with_sessions
),
-- Add operator logic
operator_logic AS (
SELECT
  stc.*,
  -- Fill operator names using window functions
  COALESCE(
    stc.operator_name,
    LAST_VALUE(
      CASE WHEN stc.event_code = 'LOGON' AND stc.operator_name IS NOT NULL THEN stc.operator_name END 
      IGNORE NULLS
    ) OVER (PARTITION BY stc.workstation_code
      ORDER BY stc.record_timestamp ASC 
      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
    )
  ) AS filled_operator_name,
  -- Track operator login state
  COALESCE(
    LAST_VALUE(
      CASE 
        WHEN stc.event_code = 'LOGON' THEN TRUE
        WHEN stc.event_code = 'LOGOFF' THEN FALSE
        ELSE NULL
      END IGNORE NULLS
    ) OVER (
      PARTITION BY stc.workstation_code
      ORDER BY stc.record_timestamp ASC, 
               CASE WHEN stc.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
      ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
    ),
    FALSE
  ) AS operator_logged_in,
  -- Next event for no-action detection
  LEAD(stc.event_code) OVER (
    PARTITION BY stc.workstation_code
    ORDER BY stc.record_timestamp ASC, 
             CASE WHEN stc.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
  ) AS next_event
FROM simple_tote_counts stc
),
-- Status calculation
status_calc AS (
SELECT
  ol.*,
  CASE
    WHEN NOT operator_logged_in THEN 'NOT_LOGGED_IN'
    WHEN event_code = 'LOGON' AND next_event = 'LOGOFF' THEN 'No Action'
    WHEN operator_logged_in AND simple_arr_count > 0 THEN 'Tote Present'
    WHEN operator_logged_in AND simple_rel_count > 0 THEN 'Blocked'
    WHEN operator_logged_in AND simple_rel_count = 0 AND simple_arr_count = 0 THEN 'Starved'
    ELSE 'Unknown'
  END AS status
FROM operator_logic ol
)

-- Show detailed breakdown
SELECT 
  record_timestamp,
  event_code,
  tote_code,
  filled_operator_name,
  operator_logged_in,
  simple_arr_count,
  simple_rel_count,
  status,
  -- Count status occurrences
  COUNT(*) OVER (PARTITION BY status) as status_count
FROM status_calc
WHERE record_timestamp >= '2025-07-25 00:00:00'
  AND record_timestamp < '2025-07-26 00:00:00'
ORDER BY record_timestamp
LIMIT 100;
