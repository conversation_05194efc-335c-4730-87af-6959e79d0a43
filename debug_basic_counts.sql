-- Very basic test to see if we're getting any tote counts at all
WITH
operators as (
 SELECT
   *
 FROM (
   SELECT
     *,
     ROW_NUMBER() OVER (PARTITION BY operator_code ORDER BY __raw_message_ingestion_time DESC) AS rn
   FROM
     `edp-p-us-east1-etl.acehardware_jeffersonga.gold_operator` 
 )
 WHERE rn = 1
),
-- Base activity events
base_activity AS (
SELECT
  TIMESTAMP(event_date_time_local) AS record_timestamp,
  event_code,
  workstation_code,
  operator_code,
  IF(IFNULL(handling_unit_code, '') = '', 'UFO', handling_unit_code) AS tote_code,
  source_system
FROM
  `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick_activity` pa
WHERE
  REGEXP_CONTAINS(workstation_code, r'GTP[0-9][0-9].*')
  AND event_timestamp_utc >= TIMESTAMP(DATETIME(DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY), TIME '00:00:00'))
  AND (
      (event_code IN ('LOGON', 'LOGOFF') AND operator_name IS NOT NULL AND operator_name != '')
      OR
      (event_code IN ('Arrival', 'Departure', 'Release') AND left(handling_unit_code,1)='D')
  )
  AND workstation_code = 'GTP01'
),
-- Add operator names and create session groups
activity_events_with_sessions AS (
SELECT
  ba.record_timestamp,
  ba.event_code,
  ba.workstation_code,
  ba.tote_code,
  ba.source_system,
  op.operator_name,    
  ROW_NUMBER() OVER (
    PARTITION BY ba.workstation_code 
    ORDER BY ba.record_timestamp ASC, CASE WHEN ba.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
  ) AS event_sequence
FROM
  base_activity ba
LEFT JOIN
  operators op
ON
  ba.operator_code = op.operator_code
),
-- MUCH SIMPLER: Just count net totes at station
simple_deltas AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    event_sequence,
    -- Net totes at station: +1 for arrival, -1 for departure
    SUM(
      CASE
        WHEN event_code = 'Arrival' THEN 1
        WHEN event_code = 'Departure' THEN -1
        ELSE 0
      END
    ) AS delta_arr_count,
    -- Blocked totes: +1 for release, -1 for departure
    SUM(
      CASE
        WHEN event_code = 'Release' THEN 1
        WHEN event_code = 'Departure' THEN -1
        ELSE 0
      END
    ) AS delta_rel_count
  FROM activity_events_with_sessions
  WHERE event_code IN ('Arrival', 'Release', 'Departure')
  GROUP BY record_timestamp, event_code, workstation_code, event_sequence
),
-- Merge with all events
events_with_counts AS (
  SELECT
    aews.record_timestamp,
    aews.event_code,
    aews.workstation_code,
    aews.source_system,
    aews.operator_name,
    aews.event_sequence,
    COALESCE(sd.delta_arr_count, 0) AS delta_arr_count,
    COALESCE(sd.delta_rel_count, 0) AS delta_rel_count
  FROM activity_events_with_sessions aews
  LEFT JOIN simple_deltas sd
    ON aews.record_timestamp = sd.record_timestamp
    AND aews.event_code = sd.event_code
    AND aews.workstation_code = sd.workstation_code
    AND aews.event_sequence = sd.event_sequence
),
-- Calculate running counts with baseline adjustment
tote_counts AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    source_system,
    operator_name,
    delta_arr_count,
    delta_rel_count,
    -- Add a baseline to handle missing historical data, then ensure non-negative
    GREATEST(0,
      100 + SUM(delta_arr_count) OVER (  -- Start with baseline of 100
        PARTITION BY workstation_code
        ORDER BY record_timestamp, event_sequence
        ROWS UNBOUNDED PRECEDING
      )
    ) AS lu_arr_count,
    -- Add a baseline for release count too
    GREATEST(0,
      50 + SUM(delta_rel_count) OVER (   -- Start with baseline of 50
        PARTITION BY workstation_code
        ORDER BY record_timestamp, event_sequence
        ROWS UNBOUNDED PRECEDING
      )
    ) AS lu_rel_count
  FROM events_with_counts
),
-- Add operator logic
operator_logic AS (
  SELECT
    tc.*,
    -- Fill operator names
    COALESCE(
      tc.operator_name,
      LAST_VALUE(
        CASE WHEN tc.event_code = 'LOGON' AND tc.operator_name IS NOT NULL THEN tc.operator_name END 
        IGNORE NULLS
      ) OVER (PARTITION BY tc.workstation_code
        ORDER BY tc.record_timestamp ASC 
        ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
      )
    ) AS filled_operator_name,
    -- Track operator login state
    COALESCE(
      LAST_VALUE(
        CASE 
          WHEN tc.event_code = 'LOGON' THEN TRUE
          WHEN tc.event_code = 'LOGOFF' THEN FALSE
          ELSE NULL
        END IGNORE NULLS
      ) OVER (
        PARTITION BY tc.workstation_code
        ORDER BY tc.record_timestamp ASC
        ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
      ),
      FALSE
    ) AS operator_logged_in,
    -- Calculate end timestamp
    LEAD(tc.record_timestamp) OVER (
      PARTITION BY tc.workstation_code
      ORDER BY tc.record_timestamp ASC
    ) AS end_timestamp,
    -- Next event
    LEAD(tc.event_code) OVER (
      PARTITION BY tc.workstation_code
      ORDER BY tc.record_timestamp ASC
    ) AS next_event
  FROM tote_counts tc
),
-- Status calculation
status_calc AS (
  SELECT
    ol.*,
    CASE
      WHEN NOT operator_logged_in THEN NULL
      WHEN event_code = 'LOGON' AND next_event = 'LOGOFF' THEN 'No Action'
      WHEN operator_logged_in AND lu_arr_count > 0 THEN 'Tote Present'
      WHEN operator_logged_in AND lu_rel_count > 0 THEN 'Blocked'
      WHEN operator_logged_in AND lu_rel_count = 0 AND lu_arr_count = 0 THEN 'Starved'
      ELSE 'Unknown'
    END AS status
  FROM operator_logic ol
)

-- Show summary of what we're getting
SELECT 
  'Event Counts' as summary_type,
  event_code as detail,
  COUNT(*) as count_value,
  '' as extra_info
FROM activity_events_with_sessions
WHERE record_timestamp >= '2025-07-25 00:00:00'
  AND record_timestamp < '2025-07-26 00:00:00'
GROUP BY event_code

UNION ALL

SELECT 
  'Status Counts' as summary_type,
  COALESCE(status, 'NULL_STATUS') as detail,
  COUNT(*) as count_value,
  '' as extra_info
FROM status_calc
WHERE record_timestamp >= '2025-07-25 00:00:00'
  AND record_timestamp < '2025-07-26 00:00:00'
GROUP BY status

UNION ALL

SELECT 
  'Count Ranges' as summary_type,
  'arr_count' as detail,
  COUNT(*) as count_value,
  CONCAT('Min: ', CAST(MIN(lu_arr_count) AS STRING), ' Max: ', CAST(MAX(lu_arr_count) AS STRING)) as extra_info
FROM status_calc
WHERE record_timestamp >= '2025-07-25 00:00:00'
  AND record_timestamp < '2025-07-26 00:00:00'
  AND lu_arr_count IS NOT NULL

UNION ALL

SELECT 
  'Count Ranges' as summary_type,
  'rel_count' as detail,
  COUNT(*) as count_value,
  CONCAT('Min: ', CAST(MIN(lu_rel_count) AS STRING), ' Max: ', CAST(MAX(lu_rel_count) AS STRING)) as extra_info
FROM status_calc
WHERE record_timestamp >= '2025-07-25 00:00:00'
  AND record_timestamp < '2025-07-26 00:00:00'
  AND lu_rel_count IS NOT NULL

ORDER BY summary_type, detail;
