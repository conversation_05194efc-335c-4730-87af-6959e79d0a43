WITH
operators as (
 SELECT
   *
 FROM (
   SELECT
     *,
     ROW_NUMBER() OVER (PARTITION BY operator_code ORDER BY __raw_message_ingestion_time DESC) AS rn
   FROM
     `edp-p-us-east1-etl.acehardware_jeffersonga.gold_operator` 
 )
 WHERE rn = 1
),
  -- Hour array CTE
  hour_array AS (
  SELECT
    workstation_code,
    Window15_Start AS Window15_Start,
    TIMESTAMP_ADD(Window15_Start, INTERVAL 900 SECOND) AS Window15_End,
  FROM
    UNNEST(`ict-p-tableau.ace_hardware_tableau_views.generate_timestamp_array_15min`( TIMESTAMP_SUB(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), HOUR), INTERVAL 30 DAY),
        TIMESTAMP_ADD(TIMESTAMP_TRUNC(CURRENT_TIMESTAMP(), DAY), INTERVAL 1425 MINUTE) )) AS Window15_Start,
    UNNEST(['GTP01','GTP02','GTP03','GTP04','GTP05','GTP06','GTP07','GTP08','GTP09','GTP10']) AS workstation_code ),
  -- Pick counts CTE
  pick_counts AS (
  SELECT DISTINCT
    TIMESTAMP(event_date_time_local) AS record_timestamp,
    `ict-p-tableau.ace_hardware_tableau_views.round_15minutes_timestamp`(TIMESTAMP(event_date_time_local)) AS hour_quarter_start_time,
    TIMESTAMP_ADD(`ict-p-tableau.ace_hardware_tableau_views.round_15minutes_timestamp`(TIMESTAMP(event_date_time_local)), INTERVAL 15 MINUTE) AS hour_quarter_end_time,
    workstation_code,
    op.operator_name,
    work_type_code,
    'GTP' AS workstation_type,
    pick_task_category,
    confirmation_reason_code AS fulfillment_status,
    source_handling_unit_code AS container_physical_code,
    source_handling_unit_type AS container_physical_type,
    destination_handling_unit_code AS pick_to_container_code,
    destination_handling_unit_type AS pick_to_container_type,
    pick_task_line_code AS line_item,
    pick_task_code AS pick_order_code,
    facility_order_code AS customer_order_code,
    picked_qty,
    shorted_qty,
    COALESCE(requested_qty, 0) AS requested_qty,
    operator_pick_time_seconds,
    gp.source_system
  FROM
    `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick` gp
  LEFT JOIN
    operators op
  ON
    gp.operator_code = op.operator_code
  WHERE
    event_timestamp_utc >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 2 DAY)
    AND REGEXP_CONTAINS(workstation_code, r'GTP[0-9][0-9].*')
    AND gp.operator_name IS NOT NULL
    AND gp.operator_name != '' ),
  -- Base activity events
  base_activity AS (
  SELECT
    TIMESTAMP(event_date_time_local) AS record_timestamp,
    event_code,
    workstation_code,
    work_type_code,
    CASE
      WHEN STARTS_WITH(workstation_code, 'GTP') THEN 'GTP'
      WHEN STARTS_WITH(workstation_code, 'CS') THEN 'QA'
      WHEN STARTS_WITH(workstation_code, 'DEC') THEN 'DECANT'
      ELSE 'UNKNOWN'
    END AS workstation_type,
    operator_code,
    -- COALESCE(handling_unit_code, 'UNKNOWN_TOTE') AS tote_code,
    IF(IFNULL(handling_unit_code, '') = '', 'UFO', handling_unit_code) AS tote_code,
    source_system,
    __raw_message_ingestion_time
  FROM
    `edp-p-us-east1-etl.acehardware_jeffersonga.gold_pick_activity` pa
  WHERE
    REGEXP_CONTAINS(workstation_code, r'GTP[0-9][0-9].*')
    AND event_timestamp_utc >= TIMESTAMP(DATETIME(DATE_SUB(CURRENT_DATE(), INTERVAL 2 DAY), TIME '00:00:00'))
    AND operator_name IS NOT NULL
    AND operator_name != '' 
    -- Potential missing filter: The reference SQL Server logic also filters where a column equivalent to
    -- `Load_Unit_Usage_Type_Code` is 'Donor'. If this column exists, its filter should be added here.
    and 
    (
        event_code IN ('LOGON', 'LOGOFF') 
    or
    event_code IN ('Arrival', 'Departure', 'Release') and left(handling_unit_code,1)='D'  
    )
  ),
  -- Add operator names and create session groups
  activity_events_with_sessions AS (
  SELECT
    ba.record_timestamp,
    ba.event_code,
    ba.workstation_code,
    ba.work_type_code,
    ba.workstation_type,
    ba.tote_code,
    ba.source_system,
    ba.__raw_message_ingestion_time,
    op.operator_name,    
    -- The SQL Server logic is continuous per workstation, not session-based.
    -- The operator's login status gates the recording of history, but not the state calculation itself.
    ROW_NUMBER() OVER (
      PARTITION BY ba.workstation_code 
      ORDER BY ba.record_timestamp ASC, CASE WHEN ba.event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
    ) AS event_sequence
  FROM
    base_activity ba
  LEFT JOIN
    operators op
  ON
    ba.operator_code = op.operator_code
  ),  
  -- **REWRITTEN TOTE STATE CALCULATION TO MATCH SQL SERVER LOGIC**
  -- This implementation replicates the SQL Server trigger logic that maintains
  -- arrival and release lists, ensuring accurate tote counting.

  -- 1. Get all tote events in chronological order
  tote_event_stream AS (
    SELECT
      workstation_code,
      tote_code,
      record_timestamp,
      event_sequence,
      event_code
    FROM activity_events_with_sessions
    WHERE event_code IN ('Arrival', 'Release', 'Departure')
  ),

  -- 2. Track the actual state of each tote following SQL Server logic
  tote_state_tracking AS (
    SELECT
      workstation_code,
      tote_code,
      record_timestamp,
      event_sequence,
      event_code,
      -- Determine current tote state based on SQL Server logic
      CASE
        WHEN event_code = 'Arrival' THEN 'Arrived'
        WHEN event_code = 'Departure' THEN NULL  -- Tote is gone, no longer tracked
        WHEN event_code = 'Release' THEN
          -- Only allow release if tote's current state is 'Arrived'
          CASE
            WHEN LAST_VALUE(
              CASE
                WHEN event_code = 'Arrival' THEN 'Arrived'
                WHEN event_code = 'Departure' THEN NULL
                ELSE NULL
              END IGNORE NULLS
            ) OVER (
              PARTITION BY workstation_code, tote_code
              ORDER BY record_timestamp, event_sequence
              ROWS BETWEEN UNBOUNDED PRECEDING AND 1 PRECEDING
            ) = 'Arrived' THEN 'Released'
            ELSE NULL  -- Invalid release, ignore
          END
        ELSE NULL
      END AS tote_state
    FROM tote_event_stream
  ),

  -- 3. Calculate workstation-level count changes for each event
  workstation_count_changes AS (
    SELECT
      workstation_code,
      record_timestamp,
      event_sequence,
      event_code,
      tote_code,
      tote_state,
      -- Calculate arrival count changes (SQL Server: LU_Arr_Count)
      CASE
        WHEN event_code = 'Arrival' THEN 1
        WHEN event_code = 'Release' AND tote_state = 'Released' THEN -1  -- Remove from arrival list
        WHEN event_code = 'Departure' THEN
          -- Remove from arrival list if tote was arrived
          CASE
            WHEN LAST_VALUE(
              CASE WHEN event_code IN ('Arrival', 'Release') THEN tote_state END IGNORE NULLS
            ) OVER (
              PARTITION BY workstation_code, tote_code
              ORDER BY record_timestamp, event_sequence
              ROWS BETWEEN UNBOUNDED PRECEDING AND 1 PRECEDING
            ) = 'Arrived' THEN -1
            ELSE 0
          END
        ELSE 0
      END AS delta_arr,
      -- Calculate release count changes (SQL Server: LU_Rel_Count)
      CASE
        WHEN event_code = 'Release' AND tote_state = 'Released' THEN 1
        WHEN event_code = 'Departure' THEN
          -- Remove from release list if tote was released
          CASE
            WHEN LAST_VALUE(
              CASE WHEN event_code IN ('Arrival', 'Release') THEN tote_state END IGNORE NULLS
            ) OVER (
              PARTITION BY workstation_code, tote_code
              ORDER BY record_timestamp, event_sequence
              ROWS BETWEEN UNBOUNDED PRECEDING AND 1 PRECEDING
            ) = 'Released' THEN -1
            ELSE 0
          END
        ELSE 0
      END AS delta_rel
    FROM tote_state_tracking
  ),

  -- 4. Aggregate count changes by workstation and timestamp
  workstation_deltas AS (
    SELECT
      workstation_code,
      record_timestamp,
      event_sequence,
      SUM(delta_arr) AS delta_arr,
      SUM(delta_rel) AS delta_rel
    FROM workstation_count_changes
    GROUP BY workstation_code, record_timestamp, event_sequence
  ),

  -- 5. Merge deltas with all events and calculate running counts
  events_with_deltas AS (
    SELECT
      aews.record_timestamp,
      aews.event_code,
      aews.workstation_code,
      aews.work_type_code,
      aews.workstation_type,
      aews.source_system,
      aews.operator_name,
      aews.event_sequence,
      COALESCE(wd.delta_arr, 0) AS delta_arr,
      COALESCE(wd.delta_rel, 0) AS delta_rel
    FROM activity_events_with_sessions aews
    LEFT JOIN workstation_deltas wd
      ON aews.workstation_code = wd.workstation_code
      AND aews.record_timestamp = wd.record_timestamp
      AND aews.event_sequence = wd.event_sequence
  ),

  -- 6. Calculate running tote counts using cumulative sum
  tote_counts AS (
    SELECT
      record_timestamp,
      event_code,
      workstation_code,
      work_type_code,
      workstation_type,
      source_system,
      operator_name,
      -- Calculate running counts (matching SQL Server LU_Arr_Count and LU_Rel_Count)
      SUM(delta_arr) OVER (
        PARTITION BY workstation_code
        ORDER BY record_timestamp, event_sequence
        ROWS UNBOUNDED PRECEDING
      ) AS lu_arr_count,
      SUM(delta_rel) OVER (
        PARTITION BY workstation_code
        ORDER BY record_timestamp, event_sequence
        ROWS UNBOUNDED PRECEDING
      ) AS lu_rel_count
    FROM events_with_deltas
  ),
  -- Add operator logic and status periods
  operator_logic AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    work_type_code,
    workstation_type,
    source_system,
    lu_arr_count,
    lu_rel_count,
    -- Fill operator names using window functions
    COALESCE(
      operator_name,
      LAST_VALUE(
        CASE WHEN event_code = 'LOGON' AND operator_name IS NOT NULL THEN operator_name END 
        IGNORE NULLS
      ) OVER (PARTITION BY workstation_code
        ORDER BY record_timestamp ASC 
        ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
      )
    ) AS operator_name,
    -- Calculate end timestamp for each event
    LEAD(record_timestamp) OVER (
      PARTITION BY workstation_code
      ORDER BY record_timestamp ASC, 
               CASE WHEN event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
    ) AS end_timestamp,
    -- Track operator login state per session
    COALESCE(
      LAST_VALUE(
        CASE 
          WHEN event_code = 'LOGON' THEN TRUE
          WHEN event_code = 'LOGOFF' THEN FALSE
          ELSE NULL
        END IGNORE NULLS
      ) OVER (
        PARTITION BY workstation_code        ORDER BY record_timestamp ASC, 
                 CASE WHEN event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
        ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
      ),
      FALSE
    ) AS operator_logged_in,
    -- Track next event for no-action detection
    LEAD(event_code) OVER (
      PARTITION BY workstation_code
      ORDER BY record_timestamp ASC, 
               CASE WHEN event_code = 'LOGOFF' THEN 1 ELSE 2 END ASC
    ) AS next_event
  FROM
    tote_counts
  ),
  -- Apply SQL Server trigger status logic (exact replication)
  status_calculation AS (
  SELECT
    record_timestamp,
    end_timestamp,
    workstation_code,
    workstation_type,
    work_type_code,
    operator_name,
    source_system,
    lu_arr_count,
    lu_rel_count,
    CASE
      WHEN NOT operator_logged_in THEN NULL
      -- Handle no action case: LOGON immediately followed by LOGOFF
      WHEN event_code = 'LOGON' AND next_event = 'LOGOFF' THEN 'No Action'
      -- SQL Server trigger logic: IF @in_LU_ArrCount > 0 THEN 'Tote Present'
      WHEN operator_logged_in AND lu_arr_count > 0 THEN 'Tote Present'
      -- SQL Server trigger logic: ELSE IF @in_LU_RelCount > 0 THEN 'Blocked'
      WHEN operator_logged_in AND lu_rel_count > 0 THEN 'Blocked'
      -- SQL Server trigger logic: ELSE IF @in_LU_RelCount = 0 AND @in_LU_ArrCount = 0 THEN 'Starved'
      WHEN operator_logged_in AND lu_rel_count = 0 AND lu_arr_count = 0 THEN 'Starved'
      ELSE 'Unknown'
    END AS status
  FROM
    operator_logic
  ),
  -- Create status periods with proper end timestamps
  status_periods AS (
  SELECT
    record_timestamp AS start_timestamp,
    COALESCE(
      end_timestamp,
      TIMESTAMP_ADD(record_timestamp, INTERVAL 4 HOUR)  -- Default end for open periods
    ) AS end_timestamp,
    workstation_code,
    workstation_type,
    work_type_code,
    operator_name,
    source_system,
    status
  FROM
    status_calculation
  WHERE
    status IS NOT NULL  -- Only include periods with operators
    AND operator_name IS NOT NULL  -- Must have an operator
  ),
  -- SIMPLIFIED: Apply status periods directly to 15-minute windows
  windowed_activity AS (
  SELECT
    q.workstation_code,
    'GTP' AS workstation_type,
    sp.operator_name,
    q.Window15_Start,
    q.Window15_End,
    `ict-p-tableau.ace_hardware_tableau_views.get_quarter_hour_code`(q.Window15_Start) AS quarter_hour_id,
    sp.work_type_code,
    sp.source_system,
    sp.status,
    -- Calculate overlap between status period and 15-minute window
    GREATEST(q.Window15_Start, sp.start_timestamp) AS overlap_start,
    LEAST(q.Window15_End, sp.end_timestamp) AS overlap_end,
    -- Calculate overlap duration in milliseconds directly
    TIMESTAMP_DIFF(
      LEAST(q.Window15_End, sp.end_timestamp),
      GREATEST(q.Window15_Start, sp.start_timestamp),
      MILLISECOND
    ) AS overlap_duration_ms
  FROM
    hour_array q
  JOIN
    status_periods sp
  ON
    q.workstation_code = sp.workstation_code
    AND sp.start_timestamp < q.Window15_End
    AND sp.end_timestamp > q.Window15_Start
    AND sp.status IS NOT NULL
  ),
  -- Calculate time metrics directly
  gtp_final AS (
  SELECT
    workstation_code,
    workstation_type,
    operator_name,
    Window15_Start,
    Window15_End,
    quarter_hour_id,
    work_type_code,
    source_system,
    -- Time metrics based on status (matching SQL Server field names)
    CASE
      WHEN status IS NOT NULL THEN overlap_duration_ms
      ELSE 0
    END AS logged_in_time_ms,
    CASE
      WHEN status = 'Starved' THEN overlap_duration_ms
      ELSE 0
    END AS starved_time_ms,
    CASE
      WHEN status = 'Blocked' THEN overlap_duration_ms
      ELSE 0
    END AS blocked_time_ms,
    CASE
      WHEN status = 'Tote Present' THEN overlap_duration_ms
      ELSE 0
    END AS presented_time_ms,
    CASE
      WHEN status = 'No Action' THEN overlap_duration_ms
      ELSE 0
    END AS no_action_time_ms
  FROM
    windowed_activity
  ),
  -- Combine GTP and pick data
  combined_data AS (
    -- GTP Activity data
    SELECT
      NULL AS record_timestamp,
      gtp.quarter_hour_id,
      gtp.Window15_Start,
      gtp.Window15_End,
      gtp.workstation_code,
      gtp.workstation_type,
      gtp.work_type_code,
      gtp.operator_name,
      STRING(NULL) AS process_type,
      STRING(NULL) AS fulfillment_status,
      STRING(NULL) AS container_physical_code,
      STRING(NULL) AS container_physical_type,
      STRING(NULL) AS pick_to_container_code,
      STRING(NULL) AS pick_to_container_type,
      STRING(NULL) AS line_item,
      STRING(NULL) AS pick_order_code,
      STRING(NULL) AS customer_order_code,
      0 AS picked_qty,
      0 AS shorted_qty,
      0 AS requested_qty,
      0 AS operator_pick_time_seconds,
      0 AS operator_operations,
      COALESCE(gtp.logged_in_time_ms, 0) AS logged_in_time_ms,
      COALESCE(gtp.starved_time_ms, 0) AS starved_time_ms,
      COALESCE(gtp.blocked_time_ms, 0) AS blocked_time_ms,
      COALESCE(gtp.presented_time_ms, 0) AS presented_time_ms,
      COALESCE(gtp.no_action_time_ms, 0) AS no_action_time_ms,
      gtp.source_system
    FROM
      gtp_final gtp
    WHERE
      ( COALESCE(gtp.starved_time_ms, 0) > 0
        OR COALESCE(gtp.blocked_time_ms, 0) > 0
        OR COALESCE(gtp.presented_time_ms, 0) > 0
        OR COALESCE(gtp.no_action_time_ms, 0) > 0
        OR COALESCE(gtp.logged_in_time_ms, 0) > 0 )
    UNION ALL
    -- Pick data
    SELECT
      pc.record_timestamp,
      `ict-p-tableau.ace_hardware_tableau_views.get_quarter_hour_code`(pc.hour_quarter_start_time) AS quarter_hour_id,
      pc.hour_quarter_start_time AS Window15_Start,
      pc.hour_quarter_end_time AS Window15_End,
      pc.workstation_code,
      pc.workstation_type,
      pc.work_type_code,
      pc.operator_name,
      pc.pick_task_category AS process_type,
      pc.fulfillment_status,
      pc.container_physical_code,
      pc.container_physical_type AS container_physical_type,
      pc.pick_to_container_code,
      pc.pick_to_container_type AS pick_to_container_type,
      pc.line_item,
      pc.pick_order_code,
      pc.customer_order_code,
      pc.picked_qty,
      pc.shorted_qty,
      pc.requested_qty,
      pc.operator_pick_time_seconds,
      1 AS operator_operations,
      0 AS logged_in_time_ms,
      0 AS starved_time_ms,
      0 AS blocked_time_ms,
      0 AS presented_time_ms,
      0 AS no_action_time_ms,
      pc.source_system
    FROM
      pick_counts pc
  ),
  -- Final cleanup with work type propagation
  final_data AS (
  SELECT
    f.quarter_hour_id,
    f.Window15_Start,
    f.Window15_End,
    f.workstation_code,
    f.workstation_type,
    f.work_type_code,
    SUBSTR( 
      REGEXP_EXTRACT( 
        MAX(STRING(COALESCE(f.record_timestamp, f.Window15_Start)) || '|' || COALESCE(f.work_type_code, '')) 
        OVER (
          PARTITION BY f.workstation_code, f.workstation_type, f.source_system 
          ORDER BY COALESCE(f.record_timestamp, f.Window15_End) ASC 
          ROWS UNBOUNDED PRECEDING
        ), 
        r'[|].+'
      ), 
      2
    ) AS work_type_code_pro,
    f.operator_name,
    f.process_type,
    f.fulfillment_status,
    f.container_physical_code,
    f.container_physical_type,
    f.pick_to_container_code,
    f.pick_to_container_type,
    f.line_item,
    f.pick_order_code,
    f.customer_order_code,
    f.picked_qty,
    f.shorted_qty,
    f.requested_qty,
    f.operator_pick_time_seconds,
    f.operator_operations,
    f.logged_in_time_ms,
    f.starved_time_ms,
    f.blocked_time_ms,
    f.presented_time_ms,
    f.no_action_time_ms,
    f.source_system
  FROM
    combined_data f
  )
-- Main output with validation
main as (
SELECT
  quarter_hour_id AS Hour_Quarter_ID,
  DATETIME(Window15_Start) AS Time,
  DATETIME(Window15_Start) AS Hour_Quarter_Start_Time,
  DATETIME(Window15_End) AS Hour_Quarter_End_Time,
  workstation_code AS Workstation_Code,
  workstation_type AS Workstation_Type,
  work_type_code_pro AS Work_Type,
  operator_name AS Operator_Code,
  process_type AS Process_Type,
  fulfillment_status AS FulfillmentStatus,
  COUNT(DISTINCT container_physical_code) AS Presentations,
  container_physical_type AS Donor_Tote_Type,
  COUNT(DISTINCT pick_to_container_code) AS Outbound_Presentations,
  pick_to_container_type AS Outbound_Tote_Type,
  COUNT(DISTINCT line_item) AS Picked_Lines,
  COUNT(DISTINCT pick_order_code) AS Pick_Orders_Worked,
  COUNT(DISTINCT customer_order_code) AS Customer_Orders_Worked,
  SUM(picked_qty) AS Picked_Qty,
  SUM(shorted_qty) AS Shorted_Qty,
  SUM(requested_qty) AS Requested_Qty,
  SUM(operator_pick_time_seconds) AS Operator_Pick_Time_Sec,
  SUM(operator_operations) AS Operator_Operations,
  CAST(LEAST(SUM(logged_in_time_ms / 1000), 900)  AS INT64) AS Logged_In_Time_Sec,
  CAST(LEAST(SUM(starved_time_ms / 1000), 900)  AS INT64) AS Starved_Time_Sec,
  CAST(LEAST(SUM(blocked_time_ms / 1000), 900)  AS INT64) AS Blocked_Time_Sec,
  CAST(LEAST(SUM(presented_time_ms / 1000), 900)  AS INT64) AS Presented_Time_Sec,
  CAST(LEAST(SUM(no_action_time_ms / 1000), 900)  AS INT64) AS No_Action_Time_Sec,
  source_system AS Source_System
FROM
  final_data
GROUP BY
  quarter_hour_id,
  DATETIME(Window15_Start),
  DATETIME(Window15_End),
  workstation_code,
  workstation_type,
  work_type_code_pro,
  operator_name,
  process_type,
  fulfillment_status,
  container_physical_type,
  pick_to_container_type,
  source_system
  )
SELECT
 DATE(TIMESTAMP_SUB(Hour_Quarter_Start_Time, INTERVAL 17 HOUR)) AS logical_day,
 SUM(Picked_lines) AS Picked_lines,
 SUM(Logged_In_Time_Sec) AS Logged_In_Time_Sec,
 SUM(Picked_lines) / (SUM(Logged_In_Time_Sec) / 3600.0) AS logged,
 100.0 * SUM(Starved_Time_Sec) / SUM(Logged_In_Time_Sec) AS starved,
 100.0 * SUM(Blocked_Time_Sec) / SUM(Logged_In_Time_Sec) AS blocked,
 100.0 * SUM(Presented_Time_Sec) / SUM(Logged_In_Time_Sec) AS presented,
 100.0 * SUM(No_Action_Time_Sec) / SUM(Logged_In_Time_Sec) AS noaction
FROM main
WHERE source_system = 'acehardware_jeffersonga_diq'
 AND workstation_type = 'GTP'
 --AND workstation_code = 'GTP10'
 AND Hour_Quarter_Start_Time >= '2025-07-15 17:00:00'
 AND Hour_Quarter_Start_Time <  '2025-08-01 17:00:00'
GROUP BY logical_day
ORDER BY logical_day;
