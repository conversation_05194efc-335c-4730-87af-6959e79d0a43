CREATE     TRIGGER [OA].[STND_OPER_WS_StarvationCalc]
ON [OA].[STND_OPER_Workstation_Status]
AFTER UPDATE
AS 
DECLARE @lInsertEvent  [BIT] = 0; 
--Populate variables with the new row (post update) information
SELECT
    @in_Timestamp    = Record_Timestamp,
    @in_Event        = Activity_Event,
	@in_Workstation  = Workstation_Code,
	@in_StationType  = Workstation_Type,
	@in_WorkType     = Work_Type_Code,
    @in_Operator     = Operator_ID,
    @in_LU_ArrList   = LU_Arr_List,
	@in_LU_ArrCount  = LU_Arr_Count,
    @in_LU_RelList   = LU_Rel_List,
    @in_LU_RelCount  = LU_Rel_Count,
    @in_SourceSystem = Source_System
FROM inserted

--Populate variables with the old row (pre update) information
SELECT
    @del_Event = Activity_Event
FROM deleted

--Goes through every different event scenario to determine operator state.
BEGIN

	-- Everything should follow this logic
	IF @in_LU_ArrCount > 0
	BEGIN
		SET @lStationEvent = 'Tote Present'
	END
	ELSE IF @in_LU_RelCount > 0 
	BEGIN
		SET @lStationEvent = 'Blocked'
	END
	ELSE IF @in_LU_RelCount = 0 AND @in_LU_ArrCount = 0 
	BEGIN
		SET @lStationEvent = 'Starved'
	END
	ELSE BEGIN
		SET @lStationEvent = 'Unknown'
	END

	IF @in_Event =  @lLogon
	BEGIN
		-- Validate there isn't another "login", if so, end the previous one, and start a new record.
		-- Doesn't matter if unless LOGON then `no action` update.
		UPDATE [OA].[STND_OPER_Workstation_Starvation]
		SET End_Timestamp = CASE WHEN DATEDIFF(HOUR, Start_Timestamp, @in_Timestamp) > @lEventCap THEN DATEADD(HOUR, @lEventCap, Start_Timestamp) ELSE @in_Timestamp END,
			Status = CASE WHEN @del_Event = @lLogon THEN 'No Action' ELSE Status END,
			End_Event = @in_Event
		WHERE End_Timestamp IS NULL and Workstation_Code = @in_Workstation and Source_System = @in_SourceSystem

		-- Event will trigger an insert.
		SET @lInsertEvent = 1

	END
	ELSE IF @in_Event =  @lLogoff
	BEGIN
		-- LOGOFF - End the events that are active.
		UPDATE [OA].[STND_OPER_Workstation_Starvation]
		SET End_Timestamp = @in_Timestamp,
			Status = CASE WHEN @del_Event = @lLogon THEN 'No Action' ELSE Status END,
			End_Event = @in_Event
		WHERE End_Timestamp IS NULL and Workstation_Code = @in_Workstation and Source_System = @in_SourceSystem

	END
	ELSE IF @in_Operator IS NOT NULL AND @in_Event IN (@lReleaseD, @lArrivalD, @lDepartureD)
	BEGIN
		-- Tote movement while someone is logged in, events can be captured.

			-- Event will trigger an insert.
			SET @lInsertEvent = 1

			-- End the previous state.
			UPDATE [OA].[STND_OPER_Workstation_Starvation]
			SET End_Timestamp = @in_Timestamp,
				End_Event = @in_Event
			WHERE End_Timestamp IS NULL AND Workstation_Code = @in_Workstation AND Source_System = @in_SourceSystem
	END

	IF @lInsertEvent = 1 
	BEGIN
		INSERT INTO [OA].[STND_OPER_Workstation_Starvation]
		(
			Start_Timestamp,
			Start_Event,
			Workstation_Code,
			Workstation_Type,
			Work_Type_Code,
			Operator_ID,
			LU_Arr_List,
			LU_Arr_Count,
			LU_Rel_List,
			LU_Rel_Count,
			Status,
			End_Timestamp,
			Source_System
		)
		VALUES
		(
			@in_Timestamp,	   
			@in_Event,
			@in_Workstation,   
			@in_StationType,   
			@in_WorkType,      
			@in_Operator,      
			@in_LU_ArrList,    
			@in_LU_ArrCount,   
			@in_LU_RelList,    
			@in_LU_RelCount,   
			@lStationEvent,    
			NULL,              
			@in_SourceSystem   
		)
	END
END


CREATE PROCEDURE [OA].[STND_OPER_WS_Fulfillment_Populate] AS 
BEGIN
    WITH
    cte_WorkstationSource AS (
        -- Get all the workstations linked with source system and the times.
        SELECT 
            STWS.Record_Timestamp,
            STWS.Workstation_ID,
            STWS.Workstation_Code,
            STWS.Workstation_Type,
            STTF.CustomerEndTimeFromDB,
            STTF.CustomerStartTime,
            STTF.Source_System 
        FROM [OA].[STND_OPER_Workstation_Status]    STWS WITH (NOLOCK)
        JOIN [OA].[STND_OPER_Timeframe]             STTF WITH (NOLOCK)
        ON STWS.Source_System = STTF.Source_System
    )
    INSERT INTO #temp_WS_Activity
    SELECT 
        [Start_Timestamp]   = WS_S.Start_Timestamp,
        [Start_HQ_ID]       = OA.Get_HQIDFromTimestamp(WS_S.Start_Timestamp),
        [End_Timestamp]     = ISNULL(WS_S.End_Timestamp, cWS.CustomerEndTimeFromDB),    -- If an event is not complete, take from the most recent ETL run.
        [End_HQ_ID]         = OA.Get_HQIDFromTimestamp(ISNULL(WS_S.End_Timestamp, cWS.CustomerEndTimeFromDB)),
        cWS.Workstation_ID,
        cWS.Workstation_Code,
        cWS.Workstation_Type,
        WS_S.Work_Type_Code,
        WS_S.Operator_ID,
        WS_S.LU_Arr_List,
        WS_S.LU_Arr_Count,
        WS_S.LU_Rel_List,
        WS_S.LU_Rel_Count,
        WS_S.Status,
        cWS.Source_System
    FROM cte_WorkstationSource cWS
    JOIN [OA].[STND_OPER_Workstation_Starvation] WS_S WITH (NOLOCK)
    ON WS_S.Source_System = cWS.Source_System
        AND WS_S.Workstation_Code = cWS.Workstation_Code
    WHERE
       WS_S.Start_Timestamp BETWEEN cWS.CustomerStartTime AND cWS.CustomerEndTimeFromDB;

    WITH 
    cte_HQ_Limited AS (
        -- Get only the HQs associated with the SourceSystems
        SELECT 
            DHQ.Hour_Quarter_ID,
            DHQ.Hour_Quarter_Start_Time,
            DHQ.Hour_Quarter_End_Time,
            DHQ.Workstation_ID,
            DHQ.Workstation_Code,
            DHQ.Workstation_Type,
            DHQ.Source_System
        FROM [OA].[STND_OPER_Workstation_HQ] DHQ  WITH (NOLOCK)
        JOIN [OA].[STND_OPER_Timeframe]      SDTF WITH (NOLOCK)
        ON DHQ.Source_System = SDTF.Source_System
        WHERE   
            Hour_Quarter_Start_Time BETWEEN SDTF.CustomerStartTime AND SDTF.CustomerEndTimeFromDB
    ), cte_HQ_Expand AS (
        -- Expand any overlapping times by hour quarter.
        SELECT 
            [Hour_Quarter_ID]           = CWH.Hour_Quarter_ID,
            [Hour_Quarter_Start_Time]   = CWH.Hour_Quarter_Start_Time,
            [Hour_Quarter_End_Time]     = CWH.Hour_Quarter_End_Time,
            [HQ_Event_Start] = 
                CASE 
                    WHEN twa.Start_Timestamp IS NULL THEN CWH.Hour_Quarter_Start_Time
                    WHEN twa.Start_Timestamp < CWH.Hour_Quarter_Start_Time THEN CWH.Hour_Quarter_Start_Time
                    ELSE twa.Start_Timestamp
                END,
            [HQ_Event_End] = 
                CASE 
                    WHEN twa.End_Timestamp IS NULL THEN CWH.Hour_Quarter_End_Time
                    WHEN twa.End_Timestamp > CWH.Hour_Quarter_End_Time THEN CWH.Hour_Quarter_End_Time
                    ELSE twa.End_Timestamp
                END,
            [Workstation_ID]            = CWH.Workstation_ID,
            [Workstation_Code]          = CWH.Workstation_Code,
            [Workstation_Type]          = CWH.Workstation_Type,
            [Work_Type]                 = twa.Work_Type_Code,
            [Operator_ID]               = twa.Operator_ID,
            [Status]                    = twa.Status,
            [Source_System]             = CWH.Source_System 
        FROM #temp_WS_Activity twa
        RIGHT OUTER JOIN cte_HQ_Limited CWH
        ON CWH.Workstation_ID = twa.Workstation_ID
            AND CWH.Source_System = twa.Source_System
        WHERE CWH.Hour_Quarter_ID BETWEEN twa.Start_HQ_ID AND twa.End_HQ_ID

    ), cte_ParseEvents AS (
    SELECT 
        [Record_Timestamp] = cHQE.HQ_Event_Start,
        [Hour_Quarter_ID] = cHQE.Hour_Quarter_ID,
        [Hour_Quarter_Start_Time] = cHQE.Hour_Quarter_Start_Time,
        [Hour_Quarter_End_Time] = cHQE.Hour_Quarter_End_Time,
        [Workstation_ID] = cHQE.Workstation_ID,
        [Workstation_Code] = cHQE.Workstation_Code,
        [Workstation_Type] = cHQE.Workstation_Type,
        [Work_Type] = cHQE.Work_Type,
        [Operator_ID] = cHQE.Operator_ID,
        [Logged_In_Time_Sec] = CASE WHEN cHQE.Status IS NOT NULL      THEN DATEDIFF(SECOND, cHQE.HQ_Event_Start, cHQE.HQ_Event_End) ELSE 0 END,
        [Starved_Time_Sec]   = CASE WHEN cHQE.Status = 'Starved'      THEN DATEDIFF(SECOND, cHQE.HQ_Event_Start, cHQE.HQ_Event_End) ELSE 0 END,
        [Blocked_Time_Sec]   = CASE WHEN cHQE.Status = 'Blocked'      THEN DATEDIFF(SECOND, cHQE.HQ_Event_Start, cHQE.HQ_Event_End) ELSE 0 END,

		-----Custom 
		[Starved_Time_Millisec] = 
         CASE  WHEN cHQE.Status = 'Starved' AND DATEDIFF(MILLISECOND, cHQE.HQ_Event_Start, cHQE.HQ_Event_End) > 2100 THEN 
		 (DATEDIFF(MILLISECOND, cHQE.HQ_Event_Start, cHQE.HQ_Event_End) - 2100)
		 ELSE 0  END,
        [Blocked_Time_Millisec]   =  CASE  WHEN cHQE.Status = 'Blocked' AND DATEDIFF(MILLISECOND, cHQE.HQ_Event_Start, cHQE.HQ_Event_End) > 500 THEN 
		(DATEDIFF(MILLISECOND, cHQE.HQ_Event_Start, cHQE.HQ_Event_End) - 500) ELSE 0  END,
		-----------------
        [Presented_Time_Sec] = CASE WHEN cHQE.Status = 'Tote Present' THEN DATEDIFF(SECOND, cHQE.HQ_Event_Start, cHQE.HQ_Event_End) ELSE 0 END,
        [No_Action_Time_Sec] = CASE WHEN cHQE.Status = 'No Action'    THEN DATEDIFF(SECOND, cHQE.HQ_Event_Start, cHQE.HQ_Event_End) ELSE 0 END,
        cHQE.Source_System 
    FROM cte_HQ_Expand cHQE
    )

    INSERT INTO #temp_WS_Each15
    SELECT * FROM cte_ParseEvents;
    
    WITH cte_Fulfillment AS (
    SELECT 
        [Record_Timestamp],
        Hour_Quarter_ID,
        Hour_Quarter_Start_Time,
        Hour_Quarter_End_Time,
        Workstation_Code,
        [Workstation_Type] = Workstation_Type,
        [Work_Type] = Work_Type_Code,
        [Operator_ID] = NULL,
        Process_Type,
        FulfillmentStatus,
        Container_Physical_Code,
        Container_Physical_Type,
        Pick_To_Container_Code,
        Pick_To_Container_Type,
        Line_Item,
        Pick_Order_Code,
        Customer_Order_Code,
        Picked_Qty,
        Shorted_Qty,
		Operator_Pick_Time_Sec,
        Requested_Qty,
        Source_System 
    FROM [OA].[STND_OPER_WS_Fulfillment_V]
    ), cte_Union AS (
    SELECT 
        cPE.Record_Timestamp,
        cPE.Hour_Quarter_ID,
        cPE.Hour_Quarter_Start_Time,
        cPE.Hour_Quarter_End_Time,
        cPE.Workstation_Code,
        cPE.Workstation_Type,
        cPE.Work_Type,
        cPE.Operator_ID,
        [Process_Type] = NULL,
        [FulfillmentStatus] = NULL,
        [Container_Physical_Code] = NULL,
        [Container_Physical_Type] = NULL,
        [Pick_To_Container_Code]  = NULL,
        [Pick_To_Container_Type] = NULL,
        [Line_Item] = NULL,
        [Pick_Order_Code] = NULL,
        [Customer_Order_Code] = NULL,
        [Picked_Qty] = 0,
        [Shorted_Qty] = 0,
		[Operator_Pick_Time_Sec]=0,
        [Requested_Qty] = 0,
        [Operator_Operations] = 0,
        cPE.Logged_In_Time_Sec,
        cPE.Starved_Time_Sec,
        cPE.Blocked_Time_Sec,
		cPE.Starved_Time_Millisec,
        cPE.Blocked_Time_Millisec,
        cPE.Presented_Time_Sec,
        cPE.No_Action_Time_Sec,
        cPE.Source_System 
    FROM #temp_WS_Each15 cPE
    UNION ALL
    SELECT 
        cF.Record_Timestamp,
        cF.Hour_Quarter_ID,
        cF.Hour_Quarter_Start_Time,
        cF.Hour_Quarter_End_Time,
        cF.Workstation_Code,
        cF.Workstation_Type,
        cF.Work_Type,
        cF.Operator_ID,
        cF.Process_Type,
        cF.FulfillmentStatus,
        cF.Container_Physical_Code,
        cF.Container_Physical_Type,
        cF.Pick_To_Container_Code,
        cF.Pick_To_Container_Type,
        cF.Line_Item,
        cF.Pick_Order_Code,
        cF.Customer_Order_Code,
        cF.Picked_Qty,
        cF.Shorted_Qty,
		cF.Operator_Pick_Time_Sec,
        cF.Requested_Qty,
        [Operator_Operations] = 1,
        [Logged_In_Time_Sec] = 0,
        [Starved_Time_Sec] = 0,
        [Blocked_Time_Sec] = 0,
		[Starved_Time_Millisec] = 0,
        [Blocked_Time_Millisec] = 0,
        [Presented_Time_Sec] = 0,
        [No_Action_Time_Sec] = 0,
        cF.Source_System 
    FROM cte_Fulfillment cF

    ), cte_EventCleanup AS (
    SELECT 
        cU.Record_Timestamp,
        cU.Hour_Quarter_ID,
        cU.Hour_Quarter_Start_Time,
        cU.Hour_Quarter_End_Time,
        cU.Workstation_Code,
        cU.Workstation_Type,
        cU.Work_Type,
        [WorkType_Pro] = 
            CASE 
                -- FulfillmentStatus = DIQ Populated | Process_Type = EMS Populated
                WHEN cU.FulfillmentStatus IS NOT NULL OR Process_Type IS NOT NULL THEN 
                CAST( SUBSTRING (
                    MAX(
                        CAST( cU.Record_Timestamp AS VARCHAR(24)) + cU.Work_Type)
                    OVER (PARTITION BY cU.Workstation_Code, cU.Workstation_Type, cU.Source_System ORDER BY ISNULL(cU.Record_Timestamp, cU.Hour_Quarter_End_Time) ASC ROWS UNBOUNDED PRECEDING)
                        , 25, 50) AS VARCHAR(50))
                WHEN cU.Work_Type IS NOT NULL THEN cU.Work_Type
                ELSE NULL
                END,
        cU.Operator_ID,
        [Operator_Pro] = 
                CASE 
                -- FulfillmentStatus = DIQ Populated | Process_Type = EMS Populated
                WHEN cU.FulfillmentStatus IS NOT NULL OR Process_Type IS NOT NULL THEN 
                    CAST( SUBSTRING (
                            MAX(
                                CAST( cU.Record_Timestamp AS VARCHAR(24)) + cU.Operator_ID)
                            OVER (PARTITION BY cU.Workstation_Code, cU.Workstation_Type, cU.Source_System ORDER BY ISNULL(cU.Record_Timestamp, cU.Hour_Quarter_End_Time) ASC ROWS UNBOUNDED PRECEDING)
                                , 25, 50) AS VARCHAR(50))
                WHEN cU.Operator_ID IS NOT NULL THEN cU.Operator_ID
                ELSE NULL
                END,
        cU.Process_Type,
        cU.FulfillmentStatus,
        cU.Container_Physical_Code,
        cU.Container_Physical_Type,
        cU.Pick_To_Container_Code,
        cU.Pick_To_Container_Type,
        cU.Line_Item,
        cU.Pick_Order_Code,
        cU.Customer_Order_Code,
        cU.Picked_Qty,
        cU.Shorted_Qty,
	    cu.Operator_Pick_Time_Sec,
        cU.Requested_Qty,
        cU.Operator_Operations,
        cU.Logged_In_Time_Sec,
        cU.Starved_Time_Sec,
        cU.Blocked_Time_Sec,
		cU.Starved_Time_Millisec,
        cU.Blocked_Time_Millisec,
        cU.Presented_Time_Sec,
        cU.No_Action_Time_Sec,
        cU.Source_System 
    FROM cte_Union cU
    )

    INSERT INTO [OA].[STND_OPER_Workstation_Fulfillment_Staging]
    (
        Hour_Quarter_ID,
        Hour_Quarter_Start_Time,
        Hour_Quarter_End_Time,
        Workstation_Code,
        Workstation_Type,
        Work_Type,
        Operator_Code,
        Process_Type,
        FulfillmentStatus,
        Presentations,
        Donor_Tote_Type,
        Outbound_Presentations,
        Outbound_Tote_Type,
        Picked_Lines,
        Pick_Orders_Worked,
        Customer_Orders_Worked,
        Picked_Qty,
        Shorted_Qty,
		Operator_Pick_Time_Sec,
        Requested_Qty,
        Operator_Operations,
        Logged_In_Time_Sec,
        Starved_Time_Sec,
        Blocked_Time_Sec,
		Starved_Time_MilliSec,
		Blocked_Time_MilliSec,
        Presented_Time_Sec,
        No_Action_Time_Sec,
        Source_System
    )

    SELECT 
        [Hour_Quarter_ID] = c_EC.Hour_Quarter_ID,
        [Hour_Quarter_Start_Time] = c_EC.Hour_Quarter_Start_Time,
        [Hour_Quarter_End_Time] = c_EC.Hour_Quarter_End_Time,
        [Workstation_Code] = c_EC.Workstation_Code,
        [Workstation_Type] = c_EC.Workstation_Type,
        [Work_Type] = c_EC.WorkType_Pro,
        [Operator_Code] = ISNULL(TU.userName,DO.Operator_Code),
        [Process_Type] = c_EC.Process_Type,
        [FulfillmentStatus] = c_EC.FulfillmentStatus,
        [Presentations] = COUNT(DISTINCT c_EC.Container_Physical_Code),
        [Donor_Tote_Type] = c_EC.Container_Physical_Type,
        [Outbound_Presentations] = COUNT(DISTINCT c_EC.Pick_To_Container_Code),
        [Outbound_Tote_Type] = c_EC.Pick_To_Container_Type,
        [Picked_Lines] = COUNT(DISTINCT c_EC.Line_Item),
        [Pick_Orders_Worked] = COUNT(DISTINCT c_EC.Pick_Order_Code),
        [Customer_Orders_Worked] = COUNT(DISTINCT c_EC.Customer_Order_Code),
        [Picked_Qty] = SUM(c_EC.Picked_Qty),
        [Shorted_Qty] = SUM(c_EC.Shorted_Qty),
		[Operator_Pick_Time_Sec] = Sum(c_EC.Operator_Pick_Time_Sec),
        [Requested_Qty] = SUM(c_EC.Requested_Qty),
        [Operator_Operations] = SUM(c_EC.Operator_Operations),
        [Logged_In_Time_Sec] = SUM(c_EC.Logged_In_Time_Sec),
        [Starved_Time_Sec] = SUM(c_EC.Starved_Time_Sec),
        [Blocked_Time_Sec] = SUM(c_EC.Blocked_Time_Sec),
		[Starved_Time_MilliSec] = SUM(c_EC.Starved_Time_MilliSec),
        [Blocked_Time_MilliSec] = SUM(c_EC.Blocked_Time_MilliSec),
        [Presented_Time_Sec] = SUM(c_EC.Presented_Time_Sec),
        [No_Action_Time_Sec] = SUM(c_EC.No_Action_Time_Sec),
        c_EC.Source_System
    FROM cte_EventCleanup c_EC
    LEFT OUTER JOIN OA.DIM_Operator DO
    ON TRY_CAST(c_EC.Operator_Pro AS BIGINT) = DO.Operator_ID
        AND DO.Source_System = c_EC.Source_System
    LEFT OUTER JOIN #tempusers TU
    ON  TRY_CAST(TU.primaryKey  AS VARCHAR) = DO.Operator_Code
        AND TU.Source_System = c_EC.Source_System
    GROUP BY c_EC.Hour_Quarter_ID,
             c_EC.Hour_Quarter_Start_Time,
             c_EC.Hour_Quarter_End_Time,
             c_EC.Workstation_Code,
             c_EC.Workstation_Type,
             c_EC.WorkType_Pro,
             ISNULL(TU.userName,DO.Operator_Code),
             c_EC.Process_Type,
             c_EC.FulfillmentStatus,
             c_EC.Container_Physical_Type,
             c_EC.Pick_To_Container_Type,
             c_EC.Source_System;

    BEGIN TRANSACTION 
        EXEC sp_rename 'OA.STND_OPER_Workstation_Fulfillment', 'STND_OPER_Workstation_Fulfillment_Secondary'
        EXEC sp_rename 'OA.STND_OPER_Workstation_Fulfillment_Staging', 'STND_OPER_Workstation_Fulfillment'
        EXEC sp_rename 'OA.STND_OPER_Workstation_Fulfillment_Secondary', 'STND_OPER_Workstation_Fulfillment_Staging'
    COMMIT TRANSACTION 
    TRUNCATE TABLE OA.STND_OPER_Workstation_Fulfillment_Staging
END


CREATE    PROCEDURE [OA].[STND_OPER_WS_Status_Update] AS 

BEGIN
    SET NOCOUNT ON;
    DECLARE @lTopN [INT] = 80000
    DECLARE @lTopQuantity INT = -1

    INSERT INTO [OA].[STND_OPER_Workstation_HQ]
         (
             [Hour_Quarter_ID],
             [Hour_Quarter_Start_Time],
             [Hour_Quarter_End_Time],
             [Workstation_ID],
             [Workstation_Code],
             [Workstation_Type],
             [Source_System]
         )
    SELECT 
        DHQ.Hour_Quarter_ID, 
        DHQ.Hour_Quarter_Start_Time, 
        DHQ.Hour_Quarter_End_Time, 
        CWS.Workstation_ID, 
        CWS.Workstation_Code, 
        CWS.Workstation_Type, 
        CWS.Source_System 
    FROM [OA].[STND_OPER_Workstation_Status] CWS WITH (NOLOCK)
    JOIN [OA].[DIM_Hour_Quarter]             DHQ WITH (NOLOCK)
        ON 1 = 1
    WHERE DHQ.Hour_Quarter_ID > OA.Get_HQIDFromTimestamp(GETDATE() - 180)
        AND CWS.Workstation_ID NOT IN (SELECT DISTINCT Workstation_ID FROM [OA].[STND_OPER_Workstation_HQ] WITH(NOLOCK))

    INSERT INTO #temp_FPA
    SELECT [RN] = ROW_NUMBER() OVER (ORDER BY Record_Timestamp ASC, CASE WHEN Event = @lLogoff THEN 1 ELSE 2 END ASC), * FROM (
        SELECT TOP (@lTopN)
        [Record_Timestamp] = FPA.Record_Timestamp,
        [Operator_ID]      = FPA.Operator_ID,
        [Load_Unit_Code]   = FPA.Load_Unit_Code,
        [Event]            = FPA.Event,
        [Workstation_ID]   = CWS.Workstation_ID,
        [Workstation_Code] = CWS.Workstation_Code,
        [Workstation_Type] = CWS.Workstation_Type,
        [Work_Type_Code]   = ISNULL(DWT.Work_Type_Code, CWS.Workstation_Type),
		[Top_Quantity]     = 
			CASE
		        WHEN CWS.Workstation_Type = 'QA'			THEN ISNULL(TRY_CAST(@lQATote AS INT), -1)
                WHEN CWS.Workstation_Type = 'AUTOSTORE'		THEN ISNULL(TRY_CAST(@lAUTOSTote AS INT), -1)
                WHEN CWS.Workstation_Type = 'MULTIPURPOSE'	THEN ISNULL(TRY_CAST(@lMULTITote AS INT), -1)
                WHEN CWS.Workstation_Type = 'DECANT'		THEN ISNULL(TRY_CAST(@lDECANTTote AS INT), -1)
                WHEN CWS.Workstation_Type = 'GTP'			THEN ISNULL(TRY_CAST(@lGTPTote AS INT), -1)
                ELSE -1
			END,
        [Source_System]    = CWS.Source_System 
    FROM [OA].[FCT_Pick_Activity] FPA WITH(NOLOCK)
    JOIN [OA].[DIM_Work_Type]     DWT WITH(NOLOCK)
    ON DWT.Work_Type_ID = FPA.Work_Type_ID
    JOIN [OA].[STND_OPER_Workstation_Status] CWS WITH(NOLOCK)
    ON CWS.Workstation_ID = FPA.Workstation_ID
    AND FPA.Record_Timestamp > CWS.Record_Timestamp
    WHERE 
	    (
            FPA.Event IN (@lArrivalD, @lReleaseD, @lDepartureD) AND Load_Unit_Usage_Type_Code = 'Donor' and left(load_unit_code,1)='D'	
        )
        OR
        (
            FPA.Event IN (@lLogon, @lLogoff)
        )
    ORDER BY FPA.Record_Timestamp ASC, CASE WHEN Event = @lLogoff THEN 1 ELSE 2 END ASC
    )tPA;

    DECLARE @WorkingString [VARCHAR](MAX);

    DECLARE @RowCnt          [BIGINT];
    DECLARE @Counter         [BIGINT] = 1;

    SELECT @RowCnt = COUNT(1) 
    from #temp_FPA

    --iterate through each row of data
    --The updates set off the Trigger, which does the calculation and logic work
    WHILE @Counter <= @RowCnt
    BEGIN
        SELECT
            @Timestamp = Record_Timestamp,
            @Event = Event,
            @Operator = Operator_ID,
            @Workstation = Workstation_Code,
            @WorkstationType = Workstation_Type,
            @WorkType = Work_Type_Code,
            @ToteCode = CASE WHEN ISNULL(Load_Unit_Code, '') = '' THEN 'UFO' ELSE Load_Unit_Code END ,
			@lTopQuantity = Top_Quantity,
            @SourceSystem = Source_System
        FROM #temp_FPA
        WHERE @Counter = RN --grab one row at a time, start at oldest event and work forward in time

        -- LOGON and LOGOFF should be only operator updating events.
        IF @Event = @lLogon
        BEGIN
            UPDATE [OA].[STND_OPER_Workstation_Status]
            SET Record_Timestamp = @Timestamp, 
                Operator_ID = @Operator, 
                Activity_Event = @Event,
                Work_Type_Code = @WorkType
            WHERE Workstation_Code = @Workstation 
                AND Source_System = @SourceSystem
        END
        ELSE IF @Event = @lLogoff
        BEGIN
            UPDATE [OA].[STND_OPER_Workstation_Status]
            SET Record_Timestamp = @Timestamp, 
                Operator_ID = NULL,
                Work_Type_Code = NULL, 
                Activity_Event = @Event
            WHERE Workstation_Code = @Workstation 
                AND Source_System = @SourceSystem
        END
        -- ARRIVAL, DEPARTURE, and RELEASE update the totes
        ELSE IF @Event = @lArrivalD
        BEGIN

            -- Arrival - add the LU to the List and then get the count.
            SELECT 
                @LU_ArrList = LU_Arr_List,
                @LU_RelList = LU_Rel_List
            FROM [OA].[STND_OPER_Workstation_Status]
            WHERE Workstation_Code = @Workstation 
                AND Source_System = @SourceSystem

            -- Return the list "minus" the current tote, if for some reason it was there "UFO".
            IF @lTopQuantity <> -1
            BEGIN

                 SELECT @LU_ArrList = STRING_AGG(value, '|')  FROM (
						SELECT TOP (@lTopQuantity - 1)
							value, ordinal
						FROM STRING_SPLIT(@LU_ArrList, '|', 1) 
						WHERE value <> @ToteCode
						ORDER BY ordinal DESC
					) q2
            END
            ELSE BEGIN
                SELECT 
                    @LU_ArrList = STRING_AGG(value, '|') 
                FROM STRING_SPLIT(@LU_ArrList, '|') 
                WHERE value <> @ToteCode
    
            END

            -- Reappend the tote, if necessary.
            SELECT @LU_ArrList = CONCAT_WS('|', @LU_ArrList, @ToteCode)

            -- Get the count.
            SELECT @LU_ArrCount = COUNT(1) FROM STRING_SPLIT(@LU_ArrList, '|')

            UPDATE [OA].[STND_OPER_Workstation_Status]
            SET Record_Timestamp = @Timestamp,
                LU_Arr_List = @LU_ArrList,
                LU_Arr_Count = @LU_ArrCount,
                Activity_Event = @Event
            WHERE Workstation_Code = @Workstation 
                AND Source_System = @SourceSystem
        END
        ELSE IF @Event = @lReleaseD
        BEGIN
            -- Release (can be blocked), remove from Arr LU add to Rel LU.

            SELECT 
                @LU_ArrList = LU_Arr_List,
                @LU_RelList = LU_Rel_List
            FROM [OA].[STND_OPER_Workstation_Status]
            WHERE Workstation_Code = @Workstation 
                AND Source_System = @SourceSystem

            -- Return the list minus the current tote.
        
            IF @lTopQuantity <> -1
            BEGIN
                 SELECT  @LU_RelList = STRING_AGG(value, '|')  FROM (
                    SELECT TOP (@lTopQuantity - 1)
                        value, ordinal
                    FROM STRING_SPLIT(@LU_RelList, '|', 1) 
                    WHERE value <> @ToteCode
                    ORDER BY ordinal DESC) q2
            END
            ELSE BEGIN
                SELECT 
                    @LU_RelList = STRING_AGG(value, '|') 
                FROM STRING_SPLIT(@LU_RelList, '|') 
                WHERE value <> @ToteCode
    
            END

            -- IF IT WAS AT THE STATION - ACTUALLY ADD TO RELEASE LIST
            IF EXISTS (SELECT TOP (1) 1 FROM STRING_SPLIT(@LU_ArrList, '|', 1) WHERE value = @ToteCode)
            BEGIN
                -- Re-add
                SELECT @LU_RelList =	CONCAT_WS('|', @LU_RelList, @ToteCode)
            END           

            -- Remove the tote from arrival if it was there.
            SELECT 
                @LU_ArrList = STRING_AGG(value, '|') 
            FROM STRING_SPLIT(@LU_ArrList, '|') 
            WHERE value <> @ToteCode

            SELECT @LU_ArrCount = COUNT(1) FROM STRING_SPLIT(@LU_ArrList, '|')
            SELECT @LU_RelCount = COUNT(1) FROM STRING_SPLIT(@LU_RelList, '|')
                       
            UPDATE [OA].[STND_OPER_Workstation_Status]
            SET Record_Timestamp = @Timestamp,
                LU_Arr_List = @LU_ArrList,
                LU_Arr_Count = @LU_ArrCount,
                LU_Rel_List = @LU_RelList,
                LU_Rel_Count = @LU_RelCount,
                Activity_Event = @Event
            WHERE Workstation_Code = @Workstation 
                AND Source_System = @SourceSystem

        END
        ELSE IF @Event = @lDepartureD
        BEGIN
            -- Departure (LU is gone), remove from Arr LU and remove from Rel LU.
            SELECT 
                @LU_ArrList = LU_Arr_List,
                @LU_RelList = LU_Rel_List
            FROM [OA].[STND_OPER_Workstation_Status]
            WHERE Workstation_Code = @Workstation 
                AND Source_System = @SourceSystem

            SELECT 
                @LU_ArrList = STRING_AGG(value, '|') 
            FROM STRING_SPLIT(@LU_ArrList, '|') 
            WHERE value <> @ToteCode

            SELECT 
                @LU_RelList = STRING_AGG(value, '|') 
            FROM STRING_SPLIT(@LU_RelList, '|') 
            WHERE value <> @ToteCode

            SELECT @LU_ArrCount = COUNT(1) FROM STRING_SPLIT(@LU_ArrList, '|')
            SELECT @LU_RelCount = COUNT(1) FROM STRING_SPLIT(@LU_RelList, '|')
                    
            UPDATE [OA].[STND_OPER_Workstation_Status]
            SET Record_Timestamp = @Timestamp,
                LU_Arr_List = @LU_ArrList,
                LU_Arr_Count = @LU_ArrCount,
                LU_Rel_List = @LU_RelList,
                LU_Rel_Count = @LU_RelCount,
                Activity_Event = @Event
            WHERE Workstation_Code = @Workstation 
                AND Source_System = @SourceSystem
        END
        SET @Counter += 1        
    END
END