-- Test the simplified counting logic
WITH sample_events AS (
  SELECT * FROM UNNEST([
    STRUCT('2025-07-25 10:00:00' AS timestamp_str, 'LOGON' AS event_code, 'GTP01' AS workstation_code, 'OP001' AS operator_name, '' AS tote_code),
    STRUCT('2025-07-25 10:01:00', 'Arrival', 'GTP01', '', 'D001'),
    STRUCT('2025-07-25 10:02:00', 'Arrival', 'GTP01', '', 'D002'),
    STRUCT('2025-07-25 10:03:00', 'Release', 'GTP01', '', 'D001'),
    STRUCT('2025-07-25 10:04:00', 'Departure', 'GTP01', '', 'D001'),
    STRUCT('2025-07-25 10:05:00', 'Departure', 'GTP01', '', 'D002'),
    STRUCT('2025-07-25 10:06:00', 'LOGOFF', 'GTP01', 'OP001', '')
  ])
),

events_with_sequence AS (
  SELECT
    TIMESTAMP(timestamp_str) AS record_timestamp,
    event_code,
    workstation_code,
    operator_name,
    CASE WHEN tote_code = '' THEN NULL ELSE tote_code END AS tote_code,
    ROW_NUMBER() OVER (ORDER BY timestamp_str) AS event_sequence
  FROM sample_events
),

-- Simple count deltas
simple_deltas AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    event_sequence,
    -- Simple arrival count: +1 for arrival, -1 for departure
    SUM(
      CASE 
        WHEN event_code = 'Arrival' THEN 1
        WHEN event_code = 'Departure' THEN -1
        ELSE 0
      END
    ) AS delta_arr_count,
    -- Simple release count: +1 for release, -1 for departure
    SUM(
      CASE 
        WHEN event_code = 'Release' THEN 1
        WHEN event_code = 'Departure' THEN -1
        ELSE 0
      END
    ) AS delta_rel_count
  FROM events_with_sequence
  WHERE event_code IN ('Arrival', 'Release', 'Departure')
  GROUP BY record_timestamp, event_code, workstation_code, event_sequence
),

-- Merge with all events
events_with_counts AS (
  SELECT
    ews.record_timestamp,
    ews.event_code,
    ews.workstation_code,
    ews.operator_name,
    ews.event_sequence,
    COALESCE(sd.delta_arr_count, 0) AS delta_arr_count,
    COALESCE(sd.delta_rel_count, 0) AS delta_rel_count
  FROM events_with_sequence ews
  LEFT JOIN simple_deltas sd
    ON ews.record_timestamp = sd.record_timestamp
    AND ews.event_code = sd.event_code
    AND ews.workstation_code = sd.workstation_code
    AND ews.event_sequence = sd.event_sequence
),

-- Calculate running counts
tote_counts AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    operator_name,
    delta_arr_count,
    delta_rel_count,
    -- Running arrival count
    SUM(delta_arr_count) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS lu_arr_count,
    -- Running release count
    SUM(delta_rel_count) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS lu_rel_count
  FROM events_with_counts
),

-- Add operator logic
operator_logic AS (
  SELECT
    tc.*,
    -- Fill operator names
    COALESCE(
      tc.operator_name,
      LAST_VALUE(
        CASE WHEN tc.event_code = 'LOGON' AND tc.operator_name IS NOT NULL THEN tc.operator_name END 
        IGNORE NULLS
      ) OVER (PARTITION BY tc.workstation_code
        ORDER BY tc.record_timestamp ASC 
        ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
      )
    ) AS filled_operator_name,
    -- Track operator login state
    COALESCE(
      LAST_VALUE(
        CASE 
          WHEN tc.event_code = 'LOGON' THEN TRUE
          WHEN tc.event_code = 'LOGOFF' THEN FALSE
          ELSE NULL
        END IGNORE NULLS
      ) OVER (
        PARTITION BY tc.workstation_code
        ORDER BY tc.record_timestamp ASC
        ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
      ),
      FALSE
    ) AS operator_logged_in,
    -- Next event
    LEAD(tc.event_code) OVER (
      PARTITION BY tc.workstation_code
      ORDER BY tc.record_timestamp ASC
    ) AS next_event
  FROM tote_counts tc
),

-- Status calculation
status_calc AS (
  SELECT
    ol.*,
    CASE
      WHEN NOT operator_logged_in THEN NULL
      WHEN event_code = 'LOGON' AND next_event = 'LOGOFF' THEN 'No Action'
      WHEN operator_logged_in AND lu_arr_count > 0 THEN 'Tote Present'
      WHEN operator_logged_in AND lu_rel_count > 0 THEN 'Blocked'
      WHEN operator_logged_in AND lu_rel_count = 0 AND lu_arr_count = 0 THEN 'Starved'
      ELSE 'Unknown'
    END AS status
  FROM operator_logic ol
)

-- Show the progression
SELECT 
  record_timestamp,
  event_code,
  filled_operator_name,
  operator_logged_in,
  delta_arr_count,
  delta_rel_count,
  lu_arr_count,
  lu_rel_count,
  status
FROM status_calc
ORDER BY record_timestamp;
