-- Simple test to verify the tote counting logic with sample data
WITH sample_events AS (
  SELECT * FROM UNNEST([
    STRUCT('2025-07-25 10:00:00' AS timestamp_str, 'Arrival' AS event_code, 'GTP01' AS workstation_code, 'D001' AS tote_code),
    STRUCT('2025-07-25 10:01:00', 'Arrival', 'GTP01', 'D002'),
    STRUCT('2025-07-25 10:02:00', 'Release', 'GTP01', 'D001'),
    STRUCT('2025-07-25 10:03:00', 'Arrival', 'GTP01', 'D003'),
    STRUCT('2025-07-25 10:04:00', 'Departure', 'GTP01', 'D001'),
    STRUCT('2025-07-25 10:05:00', 'Release', 'GTP01', 'D002'),
    STRUCT('2025-07-25 10:06:00', 'Departure', 'GTP01', 'D002')
  ])
),

events_with_sequence AS (
  SELECT
    TIMESTAMP(timestamp_str) AS record_timestamp,
    event_code,
    workstation_code,
    tote_code,
    ROW_NUMBER() OVER (ORDER BY timestamp_str) AS event_sequence
  FROM sample_events
),

-- Apply the same logic as the main query
tote_count_impacts AS (
  SELECT
    aews.record_timestamp,
    aews.event_code,
    aews.workstation_code,
    aews.event_sequence,
    aews.tote_code,
    -- Determine if this tote was previously at the station
    CASE 
      WHEN aews.event_code = 'Release' OR aews.event_code = 'Departure' THEN
        -- Check if this tote had a previous Arrival without a Departure
        CASE 
          WHEN EXISTS (
            SELECT 1 
            FROM events_with_sequence prev
            WHERE prev.workstation_code = aews.workstation_code
              AND prev.tote_code = aews.tote_code
              AND prev.event_code = 'Arrival'
              AND prev.record_timestamp < aews.record_timestamp
              AND NOT EXISTS (
                SELECT 1 
                FROM events_with_sequence dep
                WHERE dep.workstation_code = aews.workstation_code
                  AND dep.tote_code = aews.tote_code
                  AND dep.event_code = 'Departure'
                  AND dep.record_timestamp > prev.record_timestamp
                  AND dep.record_timestamp < aews.record_timestamp
              )
          ) THEN TRUE
          ELSE FALSE
        END
      ELSE FALSE
    END AS tote_was_at_station,
    
    -- Determine if this tote was previously released (for departure events)
    CASE 
      WHEN aews.event_code = 'Departure' THEN
        CASE 
          WHEN EXISTS (
            SELECT 1 
            FROM events_with_sequence rel
            WHERE rel.workstation_code = aews.workstation_code
              AND rel.tote_code = aews.tote_code
              AND rel.event_code = 'Release'
              AND rel.record_timestamp < aews.record_timestamp
              AND EXISTS (
                SELECT 1 
                FROM events_with_sequence arr
                WHERE arr.workstation_code = aews.workstation_code
                  AND arr.tote_code = aews.tote_code
                  AND arr.event_code = 'Arrival'
                  AND arr.record_timestamp < rel.record_timestamp
                  AND NOT EXISTS (
                    SELECT 1 
                    FROM events_with_sequence dep2
                    WHERE dep2.workstation_code = aews.workstation_code
                      AND dep2.tote_code = aews.tote_code
                      AND dep2.event_code = 'Departure'
                      AND dep2.record_timestamp > arr.record_timestamp
                      AND dep2.record_timestamp < rel.record_timestamp
                  )
              )
          ) THEN TRUE
          ELSE FALSE
        END
      ELSE FALSE
    END AS tote_was_released
  FROM events_with_sequence aews
),

count_deltas AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    event_sequence,
    tote_code,
    tote_was_at_station,
    tote_was_released,
    -- LU_Arr_Count changes (SQL Server logic)
    CASE 
      WHEN event_code = 'Arrival' THEN 1
      WHEN event_code = 'Release' AND tote_was_at_station THEN -1
      WHEN event_code = 'Departure' AND tote_was_at_station AND NOT tote_was_released THEN -1
      ELSE 0
    END AS delta_arr_count,
    -- LU_Rel_Count changes (SQL Server logic)
    CASE 
      WHEN event_code = 'Release' AND tote_was_at_station THEN 1
      WHEN event_code = 'Departure' AND tote_was_released THEN -1
      ELSE 0
    END AS delta_rel_count
  FROM tote_count_impacts
),

running_counts AS (
  SELECT
    record_timestamp,
    event_code,
    workstation_code,
    tote_code,
    tote_was_at_station,
    tote_was_released,
    delta_arr_count,
    delta_rel_count,
    -- Running totals
    SUM(delta_arr_count) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS lu_arr_count,
    SUM(delta_rel_count) OVER (
      PARTITION BY workstation_code 
      ORDER BY record_timestamp, event_sequence
      ROWS UNBOUNDED PRECEDING
    ) AS lu_rel_count
  FROM count_deltas
)

SELECT 
  record_timestamp,
  event_code,
  tote_code,
  tote_was_at_station,
  tote_was_released,
  delta_arr_count,
  delta_rel_count,
  lu_arr_count,
  lu_rel_count,
  -- Status based on counts (SQL Server trigger logic)
  CASE 
    WHEN lu_arr_count > 0 THEN 'Tote Present'
    WHEN lu_rel_count > 0 THEN 'Blocked'
    WHEN lu_arr_count = 0 AND lu_rel_count = 0 THEN 'Starved'
    ELSE 'Unknown'
  END AS calculated_status
FROM running_counts
ORDER BY record_timestamp;
